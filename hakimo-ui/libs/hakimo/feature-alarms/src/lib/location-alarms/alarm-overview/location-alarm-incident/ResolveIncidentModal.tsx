import {
  useResolveLocationAlarmIncident,
  useUpdateLocationAlarm,
  useUpdateAlarmGroupState,
} from '@hakimo-ui/hakimo/data-access';
import { LocationAlarmIncidentDTO, StatusType } from '@hakimo-ui/hakimo/types';
import {
  eventTracker,
  useCanUpdateLocationAlarmStatus,
  useCanUpdateAlarmGroupStatus,
  useUser,
} from '@hakimo-ui/hakimo/util';
import { Alert, Button, Modal, Textarea } from '@hakimo-ui/shared/ui-base';
import dayjs from 'dayjs';
import { useState } from 'react';

interface Props {
  product: 'scan' | 'remote-guarding';
  entityId: string;
  locationIncident?: LocationAlarmIncidentDTO;
  onStatusChange?: (status: StatusType) => void;
  onResolve?: () => void;
  onClose: () => void;
}

export function ResolveIncidentModal(props: Props) {
  const { product, entityId, locationIncident, onClose, onStatusChange, onResolve } = props;
  const [textVal, setTextVal] = useState('');

  const user = useUser();
  const canUpdateLocationAlarmStatus = useCanUpdateLocationAlarmStatus();
  const canUpdateAlarmGroupStatus = useCanUpdateAlarmGroupStatus();

  const canUpdateStatus = product === 'scan'
    ? canUpdateAlarmGroupStatus
    : canUpdateLocationAlarmStatus;

  const onSuccess = () => {
    if (product === 'scan') {
      onResolve?.();
    } else {
      onStatusChange?.(StatusType.RESOLVED);
    }
  };

  const resolveAlarmMutation = useUpdateLocationAlarm(
    entityId,
    product === 'remote-guarding' ? onSuccess : undefined
  );
  const resolveIncidentMutation = useResolveLocationAlarmIncident();
  const resolveAlarmGroupMutation = useUpdateAlarmGroupState(
    entityId,
    product === 'scan' ? onSuccess : undefined
  );

  const onTextChange = (val: string) => setTextVal(val);

  const onSubmit = () => {
    if (product === 'scan') {
      const payload = {
        resolution: 'safe' as const,
        resolutionComment: textVal,
      };
      resolveAlarmGroupMutation.mutate(payload);
    } else {
      const locationIncidentPayload = {
        incident_id: locationIncident!.id,
        incident_end_time_utc: dayjs().utc().format('YYYY-MM-DDTHH:mm:ss'),
        resolved_by: user.id,
        resolution_comment: textVal,
      };
      resolveIncidentMutation.mutate(locationIncidentPayload);

      const resolveAlarmPayload = {
        status: StatusType.RESOLVED,
        comment: `Escalation - ${textVal}`,
      };
      resolveAlarmMutation.mutate(resolveAlarmPayload);
    }
  };

  const isLoading = product === 'scan'
    ? resolveAlarmGroupMutation.isLoading
    : resolveAlarmMutation.isLoading || resolveIncidentMutation.isLoading;

  const actions = (
    <>
      {canUpdateStatus && (
        <Button
          title="Resolve"
          variant="primary"
          disabled={textVal === ''}
          onClick={onSubmit}
          loading={isLoading}
          onSideEffect={product === 'remote-guarding' ? eventTracker(
            'close_and_resolve_active_location_incident'
          ) : undefined}
        >
          Resolve
        </Button>
      )}
      <Button onClick={onClose}>Cancel</Button>
    </>
  );

  const hasError = product === 'scan'
    ? resolveAlarmGroupMutation.isError
    : resolveAlarmMutation.isError || resolveIncidentMutation.isError;

  const title = product === 'scan' ? 'Resolve Alarm Group' : 'Resolve Incident';
  const alertMessage = product === 'scan'
    ? 'Alarm group should only be resolved once the situation has been assessed and determined to be safe.'
    : 'Alarm should only be resolved once site POC and PD are alerted about the incident.';
  const commentLabel = product === 'scan'
    ? 'Provide a brief comment on the resolution'
    : 'Provide a brief comment on the escalation';
  const placeholder = product === 'scan'
    ? 'Describe the resolution and actions taken'
    : 'Describe the incident and the actions taken';

  return (
    <Modal
      title={title}
      onClose={onClose}
      open
      closable
      footer={actions}
    >
      <div className="space-y-4 p-8">
        {hasError && (
          <Alert type="error">
            {product === 'scan'
              ? 'Error resolving the alarm group'
              : 'Error submitting and resolving the incident'
            }
          </Alert>
        )}
        <Alert type="info">
          {alertMessage}
        </Alert>
        <div>{commentLabel}</div>
        <Textarea
          value={textVal}
          rows={4}
          placeholder={placeholder}
          onChange={onTextChange}
        />
      </div>
    </Modal>
  );
}

export default ResolveIncidentModal;
