import { useUpdateAlarmGroupState } from '@hakimo-ui/hakimo/data-access';
import { useCanUpdateAlarmGroupStatus, useUser } from '@hakimo-ui/hakimo/util';
import { Alert, Button, Modal, Textarea } from '@hakimo-ui/shared/ui-base';
import { useState } from 'react';

interface Props {
  alarmGroupId: string;
  onResolve?: (comment?: string) => void;
  onClose: () => void;
}

export function ResolveAlarmGroupModal(props: Props) {
  const { alarmGroupId, onClose, onResolve } = props;
  const [textVal, setTextVal] = useState('');

  const user = useUser();
  const canUpdateStatus = useCanUpdateAlarmGroupStatus();

  const onSuccess = () => {
    onResolve?.(textVal);
    onClose();
  };

  const onError = (error: Error) => {
    console.error('Error resolving alarm group:', error);
  };

  const resolveAlarmGroupMutation = useUpdateAlarmGroupState(
    alarmGroupId,
    onSuccess,
    onError
  );

  const onTextChange = (val: string) => setTextVal(val);

  const onSubmit = () => {
    const payload = {
      resolution: 'safe' as const,
      resolutionComment: textVal,
    };
    resolveAlarmGroupMutation.mutate(payload);
  };

  const actions = (
    <>
      {canUpdateStatus && (
        <Button
          title="Resolve"
          variant="primary"
          disabled={textVal === ''}
          onClick={onSubmit}
          loading={resolveAlarmGroupMutation.isLoading}
        >
          Resolve
        </Button>
      )}
      <Button onClick={onClose}>Cancel</Button>
    </>
  );

  return (
    <Modal
      title="Resolve Alarm Group"
      onClose={onClose}
      open
      closable
      footer={actions}
    >
      <div className="space-y-4 p-8">
        {resolveAlarmGroupMutation.isError && (
          <Alert type="error">
            Error resolving the alarm group
          </Alert>
        )}
        <Alert type="info">
          Alarm group should only be resolved once the situation has been assessed and determined to be safe.
        </Alert>
        <div>Provide a brief comment on the resolution</div>
        <Textarea
          value={textVal}
          rows={4}
          placeholder="Describe the resolution and actions taken"
          onChange={onTextChange}
        />
      </div>
    </Modal>
  );
}

export default ResolveAlarmGroupModal;
