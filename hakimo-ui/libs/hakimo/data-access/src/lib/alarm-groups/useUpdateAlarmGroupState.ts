import useAuthenticatedMutation from '../shared/useAuthenticatedMutation';

export interface UpdateAlarmGroupStateRequest {
  resolution: 'safe' | 'escalation_close';
  resolutionComment?: string;
}

export function useUpdateAlarmGroupState(
  alarmGroupId: string,
  onSuccess?: () => void,
  onError?: (err: Error) => void
) {
  const url = `/api/alarm_groups/${alarmGroupId}/state`;

  const request = new Request(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return useAuthenticatedMutation<null, UpdateAlarmGroupStateRequest>(
    request,
    {
      onSuccessInvalidationKeys: [
        ['alarmGroups'],
        ['alarmGroup', alarmGroupId],
        ['alarmGroupDetails', alarmGroupId],
      ],
      onSuccess,
      onError,
    }
  );
}

export default useUpdateAlarmGroupState;
