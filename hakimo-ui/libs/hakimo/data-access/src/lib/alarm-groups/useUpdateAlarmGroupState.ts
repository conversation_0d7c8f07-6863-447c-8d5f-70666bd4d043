import useAuthenticatedMutation from '../shared/useAuthenticatedMutation';

export interface UpdateAlarmGroupStateRequest {
  resolution: 'safe' | 'escalation_close';
  resolutionComment?: string;
}

export interface UpdateAlarmGroupStateResponse {
  success: boolean;
  payload: {
    message: string;
    payload: null;
    status: number;
  };
  timestamp: string;
}

export function useUpdateAlarmGroupState(
  alarmGroupId: string,
  onSuccess?: () => void,
  onError?: (err: Error) => void
) {
  const url = `http://localhost:3001/api/alarm_groups/${alarmGroupId}/state`;

  const request = new Request(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return useAuthenticatedMutation<UpdateAlarmGroupStateResponse, UpdateAlarmGroupStateRequest>(
    request,
    {
      onSuccessInvalidationKeys: [
        ['alarm_groups'],
        ['alarm_group_details', alarmGroupId],
      ],
      responseModifier: async (response) => {
        const respJson = await response.json();
        return respJson;
      },
      onSuccess,
      onError,
    }
  );
}

export default useUpdateAlarmGroupState;
