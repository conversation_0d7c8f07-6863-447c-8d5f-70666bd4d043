import { UpdateAlarmGroupStateRequest, UpdateAlarmGroupStateResponse } from '@hakimo-ui/hakimo/types';
import useAuthenticatedMutation from '../shared/useAuthenticatedMutation';

export function useUpdateAlarmGroupState(
  alarmGroupId: string,
  onSuccess?: () => void,
  onError?: (err: Error) => void
) {
  // const host = window.location.hostname;
  // const baseUrl =
  //   host === 'localhost'
  //     ? `http://${host}:${window.location.port}`
  //     : `https://event-flow-${host}`;
  const url = `http://localhost:3001/api/alarm_groups/${alarmGroupId}/state`;

  const request = new Request(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return useAuthenticatedMutation<UpdateAlarmGroupStateResponse, UpdateAlarmGroupStateRequest>(
    request,
    {
      onSuccessInvalidationKeys: [
        ['alarm_groups'],
        ['alarm_group_details', alarmGroupId],
      ],
      responseModifier: async (response) => {
        const respJson = await response.json();
        return respJson;
      },
      onSuccess,
      onError,
    }
  );
}

export default useUpdateAlarmGroupState;
