import { useAlarmGroupDetails } from '@hakimo-ui/hakimo/data-access';
import { AlarmGroupContainer } from '@hakimo-ui/hakimo/feature-shared';
import { NotFound, LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import { withAuthz, withErrorBoundary } from '@hakimo-ui/hakimo/util';
import { useParams } from 'react-router-dom';
import { ALARM_GROUP_QUERY_INTERVAL } from '../alarm-groups-list/constants';

// Define EscalationState interface locally for demo purposes
interface EscalationState {
  escalationId: string;
  alarmGroupId: string;
  cameraGroupId: string;
  tenantId: string;
  escalationOpenTimestamp: number;
  comment: string;
}

type Params = {
  alarmGroupId: string;
};

interface Props {
  alarmGroupId: string;
}

function AlarmGroupDetailsData(props: Props) {
  const { alarmGroupId } = props;

  const { data, error, isLoading } = useAlarmGroupDetails(
    alarmGroupId,
    undefined,
    false,
    ALARM_GROUP_QUERY_INTERVAL
  );

  const handleResolveEscalation = (comment?: string) => {
    console.log('Resolving escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  const handleCreateEscalation = (comment?: string) => {
    console.log('Creating escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  if (isLoading) {
    return (
      <div className="bg-onlight-bg-1 dark:bg-ondark-bg-1 fixed inset-0 flex items-center justify-center">
        <LoadingIndicator text="Loading Alarm group" />
      </div>
    );
  }

  if (error) {
    return <NotFound message="Error loading alarm group details" />;
  }

  if (!data) {
    return <NotFound message="Alarm group not found" />;
  }

  // Only show escalation for scan alarm groups
  const isScanAlarmGroup = data.product === 'scan';

  // HARDCODED FOR TESTING: Always create escalation state for scan alarm groups
  const escalationState: EscalationState | undefined = isScanAlarmGroup
    ? {
        escalationId: 'hardcoded-escalation-123',
        alarmGroupId: alarmGroupId,
        cameraGroupId: data.alarms[0]?.cameraGroupId || 'hardcoded-camera-group',
        tenantId: data.tenantId,
        escalationOpenTimestamp: Date.now() - 600000, // 10 minutes ago
        comment: 'HARDCODED: Testing escalation banner functionality',
      }
    : undefined;

  // HARDCODED FOR TESTING: Always set resolution to escalation_open for scan alarm groups
  const modifiedAlarmGroup = isScanAlarmGroup
    ? { ...data, resolution: 'escalation_open' as const }
    : data;

  return (
    <AlarmGroupContainer
      alarmGroup={modifiedAlarmGroup}
      isScanAlarmGroup={isScanAlarmGroup}
      isFullPageMode={true}
      showSOP={false}
      showExitPrompt={false}
      escalationState={escalationState}
      onResolveEscalation={isScanAlarmGroup ? handleResolveEscalation : undefined}
      onCreateEscalation={isScanAlarmGroup ? handleCreateEscalation : undefined}
    />
  );
}

function AlarmGroupDetailsRoot() {
  const { alarmGroupId } = useParams<Params>();

  if (alarmGroupId === undefined) {
    return <NotFound />;
  }

  return (
    <div className="m-8">
      <AlarmGroupDetailsData alarmGroupId={alarmGroupId} />
    </div>
  );
}

export default withAuthz(withErrorBoundary(AlarmGroupDetailsRoot), [
  'alarm_group:view',
]);
