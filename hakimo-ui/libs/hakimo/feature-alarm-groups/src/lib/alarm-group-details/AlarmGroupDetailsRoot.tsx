import { useAlarmGroupDetails } from '@hakimo-ui/hakimo/data-access';
import { AlarmGroupContainer } from '@hakimo-ui/hakimo/feature-shared';
import { NotFound, LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import { withAuthz, withErrorBoundary } from '@hakimo-ui/hakimo/util';
import { useParams } from 'react-router-dom';
import { ALARM_GROUP_QUERY_INTERVAL } from '../alarm-groups-list/constants';

// Define EscalationState interface locally for demo purposes
interface EscalationState {
  escalationId: string;
  alarmGroupId: string;
  cameraGroupId: string;
  tenantId: string;
  escalationOpenTimestamp: number;
  comment: string;
}

type Params = {
  alarmGroupId: string;
};

interface Props {
  alarmGroupId: string;
}

function AlarmGroupDetailsData(props: Props) {
  const { alarmGroupId } = props;

  const { data, error, isLoading } = useAlarmGroupDetails(
    alarmGroupId,
    undefined,
    false,
    ALARM_GROUP_QUERY_INTERVAL
  );

  const handleResolveEscalation = (comment?: string) => {
    console.log('Resolving escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  const handleCreateEscalation = (comment?: string) => {
    console.log('Creating escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  if (isLoading) {
    return (
      <div className="bg-onlight-bg-1 dark:bg-ondark-bg-1 fixed inset-0 flex items-center justify-center">
        <LoadingIndicator text="Loading Alarm group" />
      </div>
    );
  }

  if (error) {
    return <NotFound message="Error loading alarm group details" />;
  }

  if (!data) {
    return <NotFound message="Alarm group not found" />;
  }

  // Only show escalation for scan alarm groups
  const isScanAlarmGroup = data.product === 'scan';

  // Create escalation state from API data
  const escalationState: EscalationState | undefined =
    isScanAlarmGroup && data.escalations && data.escalations.length > 0
      ? {
          escalationId: data.escalations[0].id,
          alarmGroupId: alarmGroupId,
          cameraGroupId: data.alarms[0]?.cameraGroupId || 'unknown',
          tenantId: data.tenantId,
          escalationOpenTimestamp: new Date(data.escalations[0].incidentStartTimeUtc).getTime(),
          comment: data.escalations[0].description || 'Escalation in progress',
        }
      : undefined;

  // Modify the alarm group to have escalation_open resolution when there are escalations
  const modifiedAlarmGroup = isScanAlarmGroup && escalationState
    ? { ...data, resolution: 'escalation_open' as const }
    : data;

  return (
    <AlarmGroupContainer
      alarmGroup={modifiedAlarmGroup}
      isScanAlarmGroup={isScanAlarmGroup}
      isFullPageMode={true}
      showSOP={false}
      showExitPrompt={false}
      escalationState={escalationState}
      onResolveEscalation={isScanAlarmGroup ? handleResolveEscalation : undefined}
      onCreateEscalation={isScanAlarmGroup ? handleCreateEscalation : undefined}
    />
  );
}

function AlarmGroupDetailsRoot() {
  const { alarmGroupId } = useParams<Params>();

  if (alarmGroupId === undefined) {
    return <NotFound />;
  }

  return (
    <div className="m-8">
      <AlarmGroupDetailsData alarmGroupId={alarmGroupId} />
    </div>
  );
}

export default withAuthz(withErrorBoundary(AlarmGroupDetailsRoot), [
  'alarm_group:view',
]);
