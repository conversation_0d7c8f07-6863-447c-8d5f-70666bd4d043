import { useAlarmGroupDetails } from '@hakimo-ui/hakimo/data-access';
import { AlarmGroupContainer } from '@hakimo-ui/hakimo/feature-shared';
import { NotFound, LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import { withAuthz, withErrorBoundary } from '@hakimo-ui/hakimo/util';
import { useParams } from 'react-router-dom';
import { ALARM_GROUP_QUERY_INTERVAL } from '../alarm-groups-list/constants';

// Define EscalationState interface locally for demo purposes
interface EscalationState {
  escalationId: string;
  alarmGroupId: string;
  cameraGroupId: string;
  tenantId: string;
  escalationOpenTimestamp: number;
  comment: string;
}

type Params = {
  alarmGroupId: string;
};

interface Props {
  alarmGroupId: string;
}

function AlarmGroupDetailsData(props: Props) {
  const { alarmGroupId } = props;

  const { data, error, isLoading } = useAlarmGroupDetails(
    alarmGroupId,
    undefined,
    false,
    ALARM_GROUP_QUERY_INTERVAL
  );

  // Demo escalation state for testing
  const demoEscalationState: EscalationState = {
    escalationId: 'demo-escalation-123',
    alarmGroupId: alarmGroupId,
    cameraGroupId: 'demo-camera-group-456',
    tenantId: 'demo-tenant-789',
    escalationOpenTimestamp: Date.now() - 300000, // 5 minutes ago
    comment: 'Demo escalation for testing banner functionality',
  };

  const handleResolveEscalation = (comment?: string) => {
    console.log('Demo: Resolving escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  const handleCreateEscalation = (comment?: string) => {
    console.log('Demo: Creating escalation with comment:', comment);
    // In real implementation, this would call the API
  };

  if (isLoading) {
    return (
      <div className="bg-onlight-bg-1 dark:bg-ondark-bg-1 fixed inset-0 flex items-center justify-center">
        <LoadingIndicator text="Loading Alarm group" />
      </div>
    );
  }

  if (error) {
    return <NotFound message="Error loading alarm group details" />;
  }

  if (!data) {
    return <NotFound message="Alarm group not found" />;
  }

  // Only show escalation for scan alarm groups
  const isScanAlarmGroup = data.product === 'scan';

  // For demo purposes, modify the alarm group to have escalation_open resolution
  const demoAlarmGroup = isScanAlarmGroup
    ? { ...data, resolution: 'escalation_open' as const }
    : data;

  return (
    <AlarmGroupContainer
      alarmGroup={demoAlarmGroup}
      isScanAlarmGroup={isScanAlarmGroup}
      isFullPageMode={true}
      showSOP={false}
      showExitPrompt={false}
      escalationState={isScanAlarmGroup ? demoEscalationState : undefined}
      onResolveEscalation={isScanAlarmGroup ? handleResolveEscalation : undefined}
      onCreateEscalation={isScanAlarmGroup ? handleCreateEscalation : undefined}
    />
  );
}

function AlarmGroupDetailsRoot() {
  const { alarmGroupId } = useParams<Params>();

  if (alarmGroupId === undefined) {
    return <NotFound />;
  }

  return (
    <div className="m-8">
      <AlarmGroupDetailsData alarmGroupId={alarmGroupId} />
    </div>
  );
}

export default withAuthz(withErrorBoundary(AlarmGroupDetailsRoot), [
  'alarm_group:view',
]);
