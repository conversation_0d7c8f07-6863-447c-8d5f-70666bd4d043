type HttpMethod =
  | "GET"
  | "POST"
  | "PUT"
  | "DELETE"
  | "PATCH"
  | "OPTIONS"
  | "HEAD";

export interface ExtendedRequest extends Request {
  params: {
    id?: string;
    [key: string]: string | undefined;
  };
}

export type Middleware = (
  request: ExtendedRequest,
  next: () => Promise<Response>,
) => Promise<Response>;

type RouteHandler = (request: ExtendedRequest) => Promise<Response>;

interface RouteInfo {
  pattern: string;
  paramNames: string[];
  handler: RouteHandler;
}

export class Router {
  private routes: Map<string, Map<HttpMethod, RouteInfo>> = new Map();
  private globalMiddlewares: Middleware[] = [];
  private authExclusionPaths: Set<string> = new Set();
  private loggingMiddleware: Middleware | null = null;

  use(middleware: Middleware): void {
    this.globalMiddlewares.push(middleware);
  }

  useLogging(middleware: Middleware): void {
    this.loggingMiddleware = middleware;
  }

  excludeAuthForPath(path: string): void {
    this.authExclusionPaths.add(path);
  }

  get(path: string, handler: RouteHandler, ...middlewares: Middleware[]): void {
    this.register("GET", path, handler, middlewares);
  }

  post(
    path: string,
    handler: RouteHandler,
    ...middlewares: Middleware[]
  ): void {
    this.register("POST", path, handler, middlewares);
  }

  options(
    path: string,
    handler: RouteHandler,
    ...middlewares: Middleware[]
  ): void {
    this.register("OPTIONS", path, handler, middlewares);
  }

  put(path: string, handler: RouteHandler, ...middlewares: Middleware[]): void {
    this.register("PUT", path, handler, middlewares);
  }

  register(
    method: HttpMethod,
    path: string,
    handler: RouteHandler,
    middlewares: Middleware[] = [],
  ): void {
    const wrappedHandler = this.wrapHandlerWithMiddlewares(
      handler,
      middlewares,
    );

    const paramNames = this.extractParamNames(path);
    const routeInfo: RouteInfo = {
      pattern: path,
      paramNames,
      handler: wrappedHandler,
    };

    if (!this.routes.has(path)) {
      this.routes.set(path, new Map());
    }
    this.routes.get(path)?.set(method, routeInfo);
  }

  private extractParamNames(path: string): string[] {
    const paramNames: string[] = [];
    const regex = /:([^/]+)/g;
    let match: RegExpExecArray | null;
    match = regex.exec(path);
    while (match !== null) {
      paramNames.push(match[1]);
      match = regex.exec(path);
    }
    return paramNames;
  }

  private matchRoute(
    pattern: string,
    path: string,
  ): Record<string, string> | null {
    const paramNames = this.extractParamNames(pattern);
    if (paramNames.length === 0) {
      return pattern === path ? {} : null;
    }

    // Convert pattern to regex
    const regexPattern = pattern.replace(/:[^/]+/g, "([^/]+)");
    const regex = new RegExp(`^${regexPattern}$`);
    const match = path.match(regex);

    if (!match) {
      return null;
    }

    const params: Record<string, string> = {};
    paramNames.forEach((paramName, index) => {
      params[paramName] = match[index + 1];
    });

    return params;
  }

  private wrapHandlerWithMiddlewares(
    handler: RouteHandler,
    middlewares: Middleware[],
  ): RouteHandler {
    return async (request: ExtendedRequest) => {
      const url = new URL(request.url);
      const path = url.pathname;
      const allMiddlewares = this.authExclusionPaths.has(path)
        ? middlewares
        : [...this.globalMiddlewares, ...middlewares];

      const executeMiddlewares = async (index: number): Promise<Response> => {
        if (index < allMiddlewares.length) {
          const middleware = allMiddlewares[index];
          return await middleware(request, () => executeMiddlewares(index + 1));
        }
        return await handler(request);
      };
      return await executeMiddlewares(0);
    };
  }

  async handleRequest(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method.toUpperCase() as HttpMethod;

    // Handle OPTIONS requests for CORS preflight - pass through middleware
    if (method === "OPTIONS") {
      const extendedRequest = request as ExtendedRequest;
      extendedRequest.params = {};

      // Create a dummy handler that returns 200 OK
      const dummyHandler = async () => new Response(null, { status: 200 });

      // Apply middlewares to the OPTIONS request
      const wrappedHandler = this.wrapHandlerWithMiddlewares(dummyHandler, []);
      return await wrappedHandler(extendedRequest);
    }

    // Try exact match first
    const handlersForPath = this.routes.get(path);
    if (handlersForPath) {
      const routeInfo = handlersForPath.get(method);
      if (routeInfo) {
        const extendedRequest = request as ExtendedRequest;
        extendedRequest.params = {};
        return await routeInfo.handler(extendedRequest);
      }
      return new Response("Method Not Allowed", { status: 405 });
    }

    // Try pattern matching for parameterized routes
    for (const [pattern, handlersForMethod] of this.routes) {
      const params = this.matchRoute(pattern, path);
      if (params !== null) {
        const routeInfo = handlersForMethod.get(method);
        if (routeInfo) {
          const extendedRequest = request as ExtendedRequest;
          extendedRequest.params = params;
          return await routeInfo.handler(extendedRequest);
        }
        return new Response("Method Not Allowed", { status: 405 });
      }
    }

    return new Response("Not Found", { status: 404 });
  }
}
