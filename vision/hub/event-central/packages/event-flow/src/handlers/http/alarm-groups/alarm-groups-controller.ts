import {
  type AlarmGroup,
  type AlarmGroupResponse,
  type AlarmGroupStateUpdateResponse,
  type AlarmGroupUpdate,
  type CameraDetails,
  type EscalatedAlarmGroup,
  HttpServerError,
  getLogger,
} from "shared";

import { config } from "../../../util/config";

const logger = getLogger("event-flow-http-server:alarm-grups-controller");

export class AlarmGroupsController {
  async getAlarmGroups(
    searchParams: string,
    headers: Headers,
  ): Promise<AlarmGroupResponse> {
    const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/alarm_groups?${searchParams}`;

    try {
      const response = await fetch(httpServerAPI, {
        headers,
      });
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage: string;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage =
            errorData.error?.message ||
            errorData.message ||
            `HTTP error! status: ${response.status}`;
        } catch {
          errorMessage = errorText || `HTTP error! status: ${response.status}`;
        }
        throw new HttpServerError(errorMessage, response.status);
      }
      const data = await response.json();
      return data.payload;
    } catch (error) {
      if (error instanceof HttpServerError) {
        throw error;
      }
      logger.error("Failed to fetch alarm groups", { error });
      throw new HttpServerError(
        "Failed to fetch alarm groups",
        500,
        error as Error,
      );
    }
  }

  async getAlarmGroupById(
    alarmGroupId: string,
    headers: Headers,
  ): Promise<AlarmGroup | null> {
    const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/alarm_groups/${alarmGroupId}`;

    try {
      const response = await fetch(httpServerAPI, {
        headers,
      });
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage: string;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage =
            errorData.error?.message ||
            errorData.message ||
            `HTTP error! status: ${response.status}`;
        } catch {
          errorMessage = errorText || `HTTP error! status: ${response.status}`;
        }
        throw new HttpServerError(errorMessage, response.status);
      }
      const data = await response.json();
      return data.payload;
    } catch (error) {
      if (error instanceof HttpServerError) {
        throw error;
      }
      logger.error("Failed to fetch alarm group by id", { error });
      throw new HttpServerError(
        "Failed to fetch alarm group by id",
        500,
        error as Error,
      );
    }
  }

  async getAlarmGroupUpdates(
    alarmGroupId: string,
    headers: Headers,
  ): Promise<AlarmGroupUpdate[]> {
    const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/alarm_groups/${alarmGroupId}/updates`;
    try {
      const response = await fetch(httpServerAPI, {
        headers,
      });
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage: string;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage =
            errorData.error?.message ||
            errorData.message ||
            `HTTP error! status: ${response.status}`;
        } catch {
          errorMessage = errorText || `HTTP error! status: ${response.status}`;
        }
        throw new HttpServerError(errorMessage, response.status);
      }
      const data = await response.json();
      return data.payload;
    } catch (error) {
      if (error instanceof HttpServerError) {
        throw error;
      }
      logger.error("Failed to fetch alarm group updates", { error });
      throw new HttpServerError(
        "Failed to fetch alarm group updates",
        500,
        error as Error,
      );
    }
  }

  async getCurrentEscalatedAlarmGroupByTenantId(
    tenantId: string,
  ): Promise<EscalatedAlarmGroup[] | null> {
    try {
      const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/alarm_groups/escalated?tenant_id=${tenantId}`;
      const response = await fetch(httpServerAPI);
      if (!response.ok) {
        throw new HttpServerError(
          "Failed to fetch current escalated alarm group",
          response.status,
        );
      }
      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof HttpServerError) {
        throw error;
      }
      logger.error("Failed to fetch current escalated alarm group", { error });
      throw new HttpServerError(
        "Failed to fetch current escalated alarm group",
        500,
        error as Error,
      );
    }
  }

  async getCameraDetails(cameraId: string): Promise<CameraDetails> {
    const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/cameras/${cameraId}/details`;
    const response = await fetch(httpServerAPI);
    if (!response.ok) {
      throw new HttpServerError(
        "Failed to fetch camera details",
        response.status,
      );
    }
    const data = await response.json();
    return data;
  }

  async updateAlarmGroupState(
    alarmGroupId: string,
    payload: any,
    headers: Headers,
  ): Promise<AlarmGroupStateUpdateResponse> {
    const httpServerAPI = `${config.visionProtocol}://${config.visionHost}/alarm_groups/${alarmGroupId}/state`;
    const response = await fetch(httpServerAPI, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage: string;
      try {
        const errorData = JSON.parse(errorText);
        errorMessage =
          errorData.error?.message ||
          errorData.message ||
          `HTTP error! status: ${response.status}`;
      } catch {
        errorMessage = errorText || `HTTP error! status: ${response.status}`;
      }
      throw new HttpServerError(errorMessage, response.status);
    }
    const data = await response.json();
    return data;
  }
}
