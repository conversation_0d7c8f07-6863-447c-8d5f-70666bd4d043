import type { Router } from "../../../router/router";
import type { AlarmGroupsEndpoint } from "./alarm-groups-endpoint";

export function registerAlarmGroupsRoutes(
  router: Router,
  endpointHandler: AlarmGroupsEndpoint,
) {
  router.get("/api/alarm_groups", (req) => endpointHandler.getAlarmGroups(req));
  router.get("/api/alarm_groups/:id", (req) =>
    endpointHandler.getAlarmGroupById(req),
  );
  router.get("/api/alarm_groups/:id/updates", (req) =>
    endpointHandler.getAlarmGroupUpdates(req),
  );
  router.put("/api/alarm_groups/:id/state", (req) =>
    endpointHandler.updateAlarmGroupState(req),
  );
}
