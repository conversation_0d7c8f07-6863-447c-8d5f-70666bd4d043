import {
  type AlarmGroup,
  type CameraDetails,
  type EscalatedAlarmGroup,
  HttpServerError,
  HttpStatus,
  convertAlarmGroupToUnifiedAlarmGroup,
  getLogger,
} from "shared";
import type { ExtendedRequest } from "../../../router/router";
import { ApiResponse } from "../../../util/api_response";
import { AlarmGroupsController } from "./alarm-groups-controller";
const logger = getLogger("event-flow-http-server:alarm-groups-endpoint");

export class AlarmGroupsEndpoint {
  private controller: AlarmGroupsController;
  constructor() {
    this.controller = new AlarmGroupsController();
  }

  async getAlarmGroups(request: Request): Promise<Response> {
    try {
      const url = new URL(request.url);
      const headers = request.headers;

      const alarmGroupsResponse = await this.controller.getAlarmGroups(
        url.searchParams.toString(),
        headers,
      );
      const alarmGroups = alarmGroupsResponse.items;

      const cameraDetailsMap =
        await this.getCameraDetailsForAlarmGroup(alarmGroups);

      const unifiedAlarmGroups = alarmGroups.map((alarmGroup) =>
        convertAlarmGroupToUnifiedAlarmGroup(alarmGroup, cameraDetailsMap),
      );
      const ret = {
        items: unifiedAlarmGroups,
        total: alarmGroupsResponse.total,
        page: alarmGroupsResponse.page,
        pageSize: alarmGroupsResponse.page_size,
        itemsCount: alarmGroupsResponse.items_count,
      };
      return ApiResponse.success(ret);
    } catch (error) {
      if (error instanceof HttpServerError) {
        logger.error("HTTP server error in getAlarmGroups", {
          error: error.message,
          statusCode: error.statusCode,
          originalError: error.originalError,
        });
        return ApiResponse.error(
          `error while fetching data from vision http server - ${error.message}`,
          error.statusCode,
        );
      }
      logger.error("Failed to fetch alarm groups", {
        error,
        message: (error as Error).message,
        // stack: (error as Error).stack,
      });
      return ApiResponse.error(
        "Failed to fetch alarm groups",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAlarmGroupById(request: ExtendedRequest): Promise<Response> {
    try {
      const headers = request.headers;
      const alarmGroupId = request.params.id;

      if (!alarmGroupId) {
        return ApiResponse.error(
          "Alarm group ID is required",
          HttpStatus.BAD_REQUEST,
        );
      }

      const alarmGroup = await this.controller.getAlarmGroupById(
        alarmGroupId,
        headers,
      );
      if (!alarmGroup) {
        return ApiResponse.error("Alarm group not found", HttpStatus.NOT_FOUND);
      }

      const cameraDetailsMap = await this.getCameraDetailsForAlarmGroup([
        alarmGroup,
      ]);

      const tenantToEscalatedAlarmGroupsMap =
        await this.getEscalatedAlarmGroupsByTenantId(alarmGroup.tenant_id);

      const unifiedAlarmGroup = convertAlarmGroupToUnifiedAlarmGroup(
        alarmGroup,
        cameraDetailsMap,
        tenantToEscalatedAlarmGroupsMap,
      );

      return ApiResponse.success(unifiedAlarmGroup);
    } catch (error) {
      if (error instanceof HttpServerError) {
        logger.error("HTTP server error in getAlarmGroupById", {
          error: error.message,
          statusCode: error.statusCode,
          originalError: error.originalError,
        });
        return ApiResponse.error(
          `error while fetching data from vision http server - ${error.message}`,
          error.statusCode,
        );
      }
      logger.error("Failed to fetch alarm group", { error });
      return ApiResponse.error(
        "Failed to fetch alarm group",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAlarmGroupUpdates(request: ExtendedRequest): Promise<Response> {
    try {
      const headers = request.headers;
      const alarmGroupId = request.params.id;

      if (!alarmGroupId) {
        return ApiResponse.error(
          "Alarm group ID is required",
          HttpStatus.BAD_REQUEST,
        );
      }

      const updates = await this.controller.getAlarmGroupUpdates(
        alarmGroupId,
        headers,
      );
      return ApiResponse.success(updates);
    } catch (error) {
      if (error instanceof HttpServerError) {
        logger.error("HTTP server error in getAlarmGroupUpdates", {
          error: error.message,
          statusCode: error.statusCode,
          originalError: error.originalError,
        });
        return ApiResponse.error(error.message, error.statusCode);
      }
      logger.error("Failed to fetch alarm group updates", { error });
      return ApiResponse.error(
        "Failed to fetch alarm group updates",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getCameraDetailsForAlarmGroup(
    alarmGroups: AlarmGroup[],
  ): Promise<Map<string, CameraDetails>> {
    const allCameraIds = alarmGroups.reduce((acc, alarmGroup) => {
      const cameraIds = alarmGroup.events?.map((event) => event.camera_id);
      cameraIds && acc.push(...cameraIds);
      return acc;
    }, [] as string[]);
    const allCameraIdsSet = new Set(allCameraIds);
    const allCameraIdsArray = Array.from(allCameraIdsSet);
    const allCameraDetails: CameraDetails[] = [];

    for (const cameraId of allCameraIdsArray) {
      const cameraDetails = await this.controller.getCameraDetails(cameraId);
      allCameraDetails.push(cameraDetails);
    }

    const cameraDetailsMap = new Map(
      allCameraDetails.map((camera) => [camera.id, camera]),
    );
    return cameraDetailsMap;
  }

  async getEscalatedAlarmGroupsByTenantId(
    tenantId: string,
  ): Promise<Map<string, EscalatedAlarmGroup[]>> {
    const escalatedAlarmGroups =
      (await this.controller.getCurrentEscalatedAlarmGroupByTenantId(
        tenantId,
      )) ?? [];

    const tenantToEscalatedAlarmGroups = new Map<
      string,
      EscalatedAlarmGroup[]
    >();
    for (const escalatedAlarmGroup of escalatedAlarmGroups) {
      const tenantId = escalatedAlarmGroup.tenant_id;
      const escalatedAlarmGroups =
        tenantToEscalatedAlarmGroups.get(tenantId) ?? [];
      escalatedAlarmGroups.push(escalatedAlarmGroup);
      tenantToEscalatedAlarmGroups.set(tenantId, escalatedAlarmGroups);
    }
    return tenantToEscalatedAlarmGroups;
  }

  async updateAlarmGroupState(request: ExtendedRequest): Promise<Response> {
    try {
      const headers = request.headers;
      const alarmGroupId = request.params.id;
      const body = await request.json();

      if (!alarmGroupId) {
        return ApiResponse.error(
          "Alarm group ID is required",
          HttpStatus.BAD_REQUEST,
        );
      }

      const response = await this.controller.updateAlarmGroupState(
        alarmGroupId,
        body,
        headers,
      );
      return ApiResponse.success(response);
    } catch (error) {
      if (error instanceof HttpServerError) {
        logger.error("HTTP server error in updateAlarmGroupState", {
          error: error.message,
          statusCode: error.statusCode,
          originalError: error.originalError,
        });
        return ApiResponse.error(error.message, error.statusCode);
      }
      logger.error("Failed to update alarm group state", { error });
      return ApiResponse.error(
        "Failed to update alarm group state",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
