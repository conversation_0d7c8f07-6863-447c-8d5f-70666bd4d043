import type { Server as BunServer } from "bun";
import {
  type WebSocketContext,
  createWebSocketManager,
  getLogger,
} from "shared";
import { exampleHandler } from "../handlers/example";
import { healthHandler, liveHandler, readyHandler } from "../handlers/health";
import { registerAlarmGroupsRoutes } from "../handlers/http/alarm-groups";
import { AlarmGroupsEndpoint } from "../handlers/http/alarm-groups/alarm-groups-endpoint";
import { metricsHandler } from "../handlers/metrics";
import { createEventBroker } from "../lib/event-broker";
import { createLocationAlarmBroker } from "../lib/location-alarm-broker";
import { authMiddleware } from "../router/middleware/auth";
import { corsMiddleware } from "../router/middleware/cors";
import { loggingMiddleware } from "../router/middleware/logging";
import { rateLimitMiddleware } from "../router/middleware/rate-limiter";
import { Router } from "../router/router";
import { config } from "../util/config";
const logger = getLogger("event-flow:server");

export class Server {
  private eventBroker: any;
  private locationAlarmBroker: any;
  private webSocketManager = createWebSocketManager<WebSocketContext>();
  private port: number;
  private router: Router;
  private server: BunServer | null = null;

  constructor(port: number) {
    this.port = port;
    this.router = new Router();

    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeWebSocketHandlers();

    // Register signal handlers for graceful shutdown
    process.on("SIGINT", this.shutdown.bind(this));
    process.on("SIGTERM", this.shutdown.bind(this));
  }

  private initializeMiddlewares() {
    // Add CORS middleware first to handle preflight requests
    this.router.use(corsMiddleware());

    this.router.useLogging(loggingMiddleware);
    this.router.use(authMiddleware);

    // Exclude specific paths from auth middleware
    this.router.excludeAuthForPath("/metrics");
    this.router.excludeAuthForPath("/health");
    this.router.excludeAuthForPath("/health/live");
    this.router.excludeAuthForPath("/health/ready");
  }

  private initializeWebSocketHandlers() {
    for (const product of config.products) {
      if (product === "scan") {
        this.eventBroker = createEventBroker();
        this.eventBroker.run();
        this.webSocketManager.registerHandler(
          "/events",
          this.eventBroker.webSocketHandler,
        );
      }
      if (product === "remote-guarding") {
        this.locationAlarmBroker = createLocationAlarmBroker();
        this.webSocketManager.registerHandler(
          "/locationalarms",
          this.locationAlarmBroker.webSocketHandler,
        );
      }
    }
  }

  private initializeRoutes() {
    this.router.get("/metrics", metricsHandler);
    this.router.get("/health", healthHandler);
    this.router.get("/example", exampleHandler, rateLimitMiddleware);
    this.router.get("/health/live", liveHandler);
    this.router.get("/health/ready", readyHandler);
    if (config.products.includes("scan")) {
      registerAlarmGroupsRoutes(this.router, new AlarmGroupsEndpoint());
    }
  }

  private handleWebSocketUpgrade(request: Request, server: BunServer): boolean {
    const url = new URL(request.url);
    const path = url.pathname;

    if (this.webSocketManager.getRegisteredPaths().includes(path)) {
      const upgraded = server.upgrade(request, {
        data: {
          request,
          authToken:
            request.headers.get("Authorization") ??
            (request.headers.get("Sec-Websocket-Protocol") || ""),
        },
      });

      if (!upgraded) {
        throw new Error("WebSocket upgrade failed");
      }

      return true;
    }

    return false;
  }

  private async handleRequest(
    request: Request,
    server: BunServer,
  ): Promise<Response> {
    try {
      if (this.handleWebSocketUpgrade(request, server)) {
        return new Response(null, { status: 101 });
      }
      return await this.router.handleRequest(request);
    } catch (error) {
      logger.error("❌ Error handling request:", error);
      return new Response("Internal Server Error", { status: 500 });
    }
  }

  public start(): void {
    try {
      this.server = Bun.serve({
        fetch: (request: Request, server: BunServer) =>
          this.handleRequest(request, server),
        websocket: this.webSocketManager.rootHandler,
        port: this.port,
        idleTimeout: 0, // with 0 it means no timeout limit
      });

      logger.info(`🚀 Server is running on port ${this.port}`);
    } catch (error) {
      logger.error("❌ Failed to start server:", error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    try {
      logger.info("🛑 Shutting down server gracefully...");

      // First step: Stop accepting new connections but keep existing ones
      if (this.server) {
        this.server.stop(false); // false means don't force-close existing connections
      }

      // Add a timeout for graceful shutdown
      const shutdownTimeout = setTimeout(() => {
        logger.warn("⚠️ Shutdown timed out, forcing exit");
        process.exit(1);
      }, 25000); // 25 seconds (less than K8s default 30s grace period)

      // Clear the timeout if shutdown completes
      shutdownTimeout.unref(); // Don't let this timeout prevent process exit

      // Second step: Await all async cleanup operations
      if (config.products.includes("scan")) {
        await this.eventBroker.shutdown();
      }
      // await this.locationAlarmBroker.shutdown();

      // Third step: Now force-close any remaining connections
      if (this.server) {
        this.server.stop(true);
      }

      // Clear the timeout since we completed successfully
      clearTimeout(shutdownTimeout);

      logger.info("✅ Server has been shut down.");
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1); // Exit with error code on shutdown failures
    }
  }
}
