import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import type { Redis } from "ioredis";
import {
  type AlarmMediaResponse,
  type DetectionEvent,
  type Escalation,
  type EventType,
  Product,
  type UnifiedAlarm,
  type UnifiedAlarmGroup,
  type UnifiedLocation,
  getLogger,
} from "shared";
import { metrics } from "../../types/metrics";
import { config } from "../../util/config";
import UserConnectionManager from "../user-connection-manager";
import type { EventHandler } from "./event-handler";
const logger = getLogger("event-flow:abstract-event-handler");

type SeverityLevel = "low" | "medium" | "high" | "not_known";

const NOT_KNOWN_SEVERITY = "not_known";

interface EventData {
  alarm_group_id: string;
  tenant_id: string;
  camera_group_id: string;
  camera_id: string;
  camera_name: string;
  severity: string;
  operator_id: string;
}

export interface PartialLocationAlarmIncidentDTO {
  location_id?: number;
  tenant_id?: string;
  title?: string;
  description?: string;
  created_by?: string;
  resolved_by?: string;
}
export interface LocationAlarmIncidentDTO
  extends PartialLocationAlarmIncidentDTO {
  id?: number;
  incidentStartTimeUtc?: string;
  incidentEndTimeUtc?: string;
  initiatingLocationAlarmId?: string;
}

export interface LocationAlarmsDTO {
  total: number;
  items: LocationAlarm[];
}

export interface MotionAlarm {
  id: string;
  timestamp: string;
  videoStartTime?: string;
  videoEndTime?: string;
  sourceEntity: {
    id: string;
    name?: string;
    audio_devices?: Array<{ id: string }>;
    livestreamUrl?: string;
  };
  status?: string;
}

export const locationAlarmStatuses = [
  "Analyzing",
  "Pending",
  "Resolved",
  "In Progress",
] as const;

export interface GatewayResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export type LocationAlarmStatus = (typeof locationAlarmStatuses)[number];

export interface Location {
  id: string | number;
  name: string;
  city?: string;
  state?: string;
  country?: string;
  description?: string;
  tenant_id: string;
  timezone?: string;
}

export interface LocationAlarm {
  id: string;
  guid: string;
  alarmTime: string;
  status: LocationAlarmStatus;
  location: Location;
  tap: number;
  rawAlarms: MotionAlarm[];
  locationIncidents?: LocationAlarmIncidentDTO[];
}

export interface AlarmVideoDetails {
  markers: AlarmVideoMarker[];
  startTime: string | null;
}
export interface AlarmVideoMarker {
  timestamp: number; // time in seconds since start of video
  label: string; // string to be displayed
  type: "Error" | "Info" | "Warning";
}

export enum BoxType {
  PERSON = "PERSON",
  VEHICLE = "MOVING VEHICLE",
}
export interface Box {
  frame: number; // -1 if applicable to entire video
  coords: number[]; // Top left x, y, width, height
  type?: BoxType;
}
export interface AlarmMedia {
  videoPath?: string;
  imagePath?: string;
  videoDetails?: AlarmVideoDetails;
  boxes?: Box[];
}
export interface ReducedAlarm {
  id: string;
  type: string;
  timestamp: string;
}

export const statuses = [
  "Resolved by Hakimo",
  "Analyzing",
  "Pending",
  "Resolved Manually",
  "Alarm Acknowledged via ACS",
  "In Progress",
] as const;

export type AlarmStatus = (typeof statuses)[number];

export interface Linkable {
  id: string;
  name: string;
}
export interface SourceEntity extends Linkable {
  type: "CAMERA" | "DOOR";
}
export interface Employee extends Linkable {
  phoneNumber: string;
}

export interface AlarmMetadata {
  badgeNumber: string;
}

export interface Alarm extends ReducedAlarm {
  status: AlarmStatus;
  tap: number | null;
  sourceSystem: string;
  location?: Location;
  sourceEntity?: SourceEntity;
  tags?: string[];
  employee?: Employee;
  localTime?: string;
  timeToRemediationInSeconds?: number;
  resolvedAt?: string;
  camera?: string;
  videoPath?: string;
  sop?: string;
  imagePath?: string;
  floorplanPath?: string;
  videoDetails?: AlarmVideoDetails;
  tenant: Linkable;
  metadata?: AlarmMetadata;
  videoStartTime?: string;
  videoEndTime?: string;
}

export interface AlarmsDTO {
  total: number;
  items: Alarm[];
}

export interface EscalationDTO {
  alarmGroupIds: string[];
  tenantId: string;
  cameraGroupId: string;
  escalationId: string;
  escalationOpenTimestamp: number;
  comment: string;
}
export abstract class BaseEventHandler {
  protected redisClient: Redis;
  // protected rabbitMQClient: RabbitMQClient;
  protected eventHandler: EventHandler;
  protected orphan_low_alarm_queue = "orphan_low_alarm_queue";
  protected orphan_high_alarm_queue = "orphan_high_alarm_queue";
  protected orphan_escalation_queue = "orphan_escalation_queue";
  protected readonly userKeyPrefix: string = "user_status:";
  protected readonly onlineUsersKey: string = "online_users";
  // private readonly operatorAlarmsKeyPrefix: string = "operator_alarms:";
  private readonly expirySeconds = {
    one_hr: 60 * 60,
    twelve_hrs: 60 * 60 * 12,
    one_day: 60 * 60 * 24,
  };

  protected severityToQueue: Record<string, string> = {
    low: "low_severity_queue",
    medium: "low_severity_queue",
    high: "high_severity_queue",
    not_known: "low_severity_queue",
  };

  private readonly alarmMediaCachePrefix: string = "alarm_media_cache:";
  private readonly alarmMediaCacheExpirySeconds = 5 * 60; // 5 minutes in seconds

  constructor(redisClient: Redis, eventHandler: EventHandler) {
    this.redisClient = redisClient;
    this.eventHandler = eventHandler;
    // this.rabbitMQClient = new RabbitMQClient(rabbitMQConfig);
    // this.rabbitMQClient.connect();
  }

  // Common method for logging
  protected logEvent(eventType: EventType, payload: any) {
    // logger.info(`[${eventType}] Event received:`, payload);
  }

  /**
   * Checks if frames in a detection event contain hex images
   * @param event The detection event to check
   * @returns true if frames contain hex images, false if they contain S3 URLs
   */
  protected async hasHexImages(event: DetectionEvent): Promise<boolean> {
    // If no metadata or frames, return false
    if (
      !event.metadata?.["frames"] ||
      !Array.isArray(event.metadata["frames"]) ||
      event.metadata["frames"].length === 0
    ) {
      return false;
    }

    // Take the first frame's data to determine the type
    const firstFrameData = event.metadata["frames"][0][0];

    // S3 URLs typically start with https:// and contain .s3.amazonaws.com/
    if (
      firstFrameData.startsWith("https://") &&
      firstFrameData.includes(".s3.amazonaws.com/")
    ) {
      return false;
    }

    // Hex images are typically hex strings (contain only hexadecimal characters)
    const hexRegex = /^[0-9A-Fa-f]+$/;
    return hexRegex.test(firstFrameData);
  }

  protected async marshelEscalationDTOs(
    allEscalations: Record<string, string>,
  ): Promise<EscalationDTO[]> {
    return Object.entries(allEscalations).map(([_, value]) => {
      return JSON.parse(value) as EscalationDTO;
    });
  }

  protected async publishEscalationEventToActiveUsers() {
    const operators = await this.getAllOnlineOperators();
    const allEscalations = await this.redisClient.hgetall("global:escalations");
    const escalationDTOs = await this.marshelEscalationDTOs(allEscalations);
    if (operators.length > 0) {
      const connectionManager = UserConnectionManager.getInstance();
      for (const fellowOperatorId of operators) {
        const connection = connectionManager.getConnection(fellowOperatorId);
        if (connection) {
          const escalation_notification = {
            event_type: "get_escalations",
            escalations: escalationDTOs,
          };
          connection.send(JSON.stringify(escalation_notification));
        }
      }
    }
  }

  protected async getOperatorForAlarmGroup(
    alarm_group_id: string,
  ): Promise<string | null> {
    const operator_key = `alarm_operator:${alarm_group_id}`;
    return await this.redisClient.get(operator_key);
  }

  protected async getAnotherAlarmId(
    alarm_group_id: string,
    tenant_id: string,
    camera_group_id: string,
  ): Promise<string | null> {
    const severity_key = `alarm_group_severity:${alarm_group_id}`;
    const severity = await this.redisClient.hget(severity_key, "severity");
    let severity_for_new_id = null;
    if (severity === "high") {
      severity_for_new_id = "low";
    } else {
      severity_for_new_id = "high";
    }
    const another_alarm_group_id = this.getAlarmGroupForEvent(
      camera_group_id,
      tenant_id,
      severity_for_new_id,
    );
    return another_alarm_group_id;
  }

  protected async createDetectionEventKey(
    alarm_group_id: string,
    camera_group_id: string,
    tenant_id: string,
    severity: string,
  ): Promise<string> {
    return `alarm_events:${alarm_group_id}:${camera_group_id}:${tenant_id}:${severity}`;
  }

  protected async createDetectionEventKeyV1(
    alarm_group_id: string,
    camera_group_id: string,
    tenant_id: string,
  ): Promise<string> {
    return `alarm_group_events:${alarm_group_id}:${camera_group_id}:${tenant_id}`;
  }

  protected async createHistoryDetectionEventKey(
    alarm_group_id: string,
    camera_group_id: string,
    tenant_id: string,
    severity: string,
  ): Promise<string> {
    return `alarm_events_history:${alarm_group_id}:${camera_group_id}:${tenant_id}:${severity}`;
  }

  protected async getAlarmGroupForEvent(
    group_id: string,
    tenant_id: string,
    severity: string,
    event_timestamp_utc?: number,
  ): Promise<string | null> {
    const key = `alarm_group:${group_id}:${tenant_id}:${severity}`;

    // First, try to get the alarm_group_id from Redis
    const alarmGroupId = await this.redisClient.hget(key, "alarm_group_id");

    // If we found an alarm_group_id in Redis, return it
    if (alarmGroupId) {
      return alarmGroupId;
    }

    // If we don't have a timestamp, we can't create a new alarm group
    if (!event_timestamp_utc) {
      logger.warn("Cannot create alarm group without event_timestamp_utc", {
        group_id,
        tenant_id,
        severity,
      });
      return null;
    }

    // Make API call to gateway to create the event and get the alarm_group_id
    return await this.createAlarmGroup(
      group_id,
      tenant_id,
      severity,
      event_timestamp_utc,
    );
  }

  /**
   * Creates a new alarm group by making a POST request to the gateway
   * @param camera_group_id The camera group ID
   * @param tenant_id The tenant ID
   * @param severity The severity level
   * @param event_timestamp_utc The timestamp of the event in UTC
   * @returns The created alarm group ID or null if the request failed
   */
  protected async createAlarmGroup(
    camera_group_id: string,
    tenant_id: string,
    severity: string,
    event_timestamp_utc: number,
  ): Promise<string | null> {
    try {
      // Determine if we should use HTTP or HTTPS based on the host
      const protocol = config.visionProtocol;
      const gatewayUrl = `${protocol}://${config.visionHost}/camera_group/${camera_group_id}/tenant/${tenant_id}/severity/${severity}`;

      const payload = {
        timestamp_utc: String(event_timestamp_utc),
      };

      logger.info("Attempting to create alarm group", {
        url: gatewayUrl,
        camera_group_id,
        tenant_id,
        severity,
      });

      const response = await fetch(gatewayUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        logger.error("Failed to create alarm group:", {
          statusCode: response.status,
          statusText: response.statusText,
          camera_group_id,
          tenant_id,
          severity,
        });
        return null;
      }

      const data = await response.json();

      // Store the alarm_group_id in Redis for future reference
      const key = `alarm_group:${camera_group_id}:${tenant_id}:${severity}`;
      await this.redisClient.hset(key, "alarm_group_id", data.alarm_group_id);
      // Set expiry for 1 day (86400 seconds)
      await this.redisClient.expire(key, 86400);

      return data.alarm_group_id;
    } catch (error) {
      logger.error("Error creating alarm group:", {
        error: error instanceof Error ? error.message : String(error),
        camera_group_id,
        tenant_id,
        severity,
        config: config.visionHost,
      });
      return null;
    }
  }

  protected async getOperatorForAlarm(
    alarm_group_id: string,
  ): Promise<string | null> {
    const hashKey = "alarms:operators";
    return await this.redisClient.hget(hashKey, alarm_group_id);
  }

  protected async removeAllEventsForKey(eventData: {
    alarm_group_id: string;
    group_id: string;
    tenant_id: string;
    severity: string;
  }) {
    const key = await this.createDetectionEventKey(
      eventData.alarm_group_id,
      eventData.group_id,
      eventData.tenant_id,
      eventData.severity,
    );

    // Remove all elements in the sorted set for the given key
    await this.redisClient.zremrangebyscore(key, "-inf", "+inf");
  }

  /**
   * Acquires a distributed lock using Redis
   * @param lockKey The key for the lock
   * @param ttlSeconds Time to live in seconds
   * @returns True if the lock was acquired, false otherwise
   */
  protected async acquireLock(
    lockKey: string,
    ttlSeconds: number,
  ): Promise<boolean> {
    // Use SET with NX option to ensure atomicity
    const result = await this.redisClient.set(
      lockKey,
      "locked",
      "EX",
      ttlSeconds,
      "NX",
    );
    return result === "OK";
  }

  /**
   * Releases a distributed lock
   * @param lockKey The key for the lock
   */
  protected async releaseLock(lockKey: string): Promise<void> {
    await this.redisClient.del(lockKey);
  }

  /**
   * Migrates events from temporary Redis keys to final keys
   * @param group_id The group ID
   * @param tenant_id The tenant ID
   * @param severity The severity level
   * @param alarm_group_id The alarm group ID to associate with the events
   * @returns The number of events migrated
   */
  protected async migrateTemporaryEvents(
    group_id: string,
    tenant_id: string,
    severity: string,
    alarm_group_id: string,
  ): Promise<number> {
    const tempKey = `temp_events:${group_id}:${tenant_id}:${severity}`;
    const lockKey = `migration_lock:${group_id}:${tenant_id}:${severity}`;

    // Try to acquire a lock with a 10-second expiration
    const lockAcquired = await this.acquireLock(lockKey, 10);
    if (!lockAcquired) {
      console.log(`Migration already in progress for ${tempKey}, skipping`);
      return 0;
    }

    try {
      // Get all events from the temporary key
      const events = await this.redisClient.zrange(tempKey, 0, -1);
      let migratedCount = 0;

      for (const eventJson of events) {
        try {
          const event = JSON.parse(eventJson) as DetectionEvent;
          event.alarm_group_id = alarm_group_id;

          // Add to final destination
          const key = await this.createDetectionEventKey(
            alarm_group_id,
            group_id,
            tenant_id,
            severity,
          );
          const score = event.event_timestamp_utc;
          const value = JSON.stringify(event);
          await this.redisClient.zadd(key, score, value);
          migratedCount++;
        } catch (error) {
          console.error(`Failed to migrate event: ${error}`);
        }
      }

      // Remove the temporary key after migration
      await this.redisClient.del(tempKey);
      return migratedCount;
    } finally {
      // Always release the lock, even if an error occurs
      await this.releaseLock(lockKey);
    }
  }

  // Store the status of an operator (online/offline) for a given tenant
  protected async setOperatorStatus(
    operator_id: string,
    state: "online" | "offline",
  ): Promise<void> {
    const userKey = `${operator_id}`;
    const now = Date.now();

    if (state === "online") {
      // Add to sorted set with current timestamp as score
      await this.redisClient.zadd("online_operators", now, userKey);
    } else {
      // Remove from sorted set
      await this.redisClient.zrem("online_operators", userKey);
    }
  }

  protected async getAllOnlineOperators(): Promise<string[]> {
    // Get all operators from the sorted set
    return await this.redisClient.zrange("online_operators", 0, -1);
  }

  // You can also get active users within a time window
  protected async getRecentlyActiveOperators(
    timeWindowMs = 300000,
  ): Promise<string[]> {
    const cutoffTime = Date.now() - timeWindowMs;
    return await this.redisClient.zrangebyscore(
      "online_operators",
      cutoffTime,
      "+inf",
    );
  }

  protected async getItemsFromQueue(
    queue: string,
    count: number,
  ): Promise<EventData[]> {
    if (count <= 0) {
      return [];
    }
    try {
      const parsedItems: EventData[] = [];
      for (let i = 0; i < count; i++) {
        const item = await this.redisClient.lpop(queue);
        if (!item) {
          // If the queue is empty, break the loop
          break;
        }

        try {
          parsedItems.push(JSON.parse(item));
        } catch (error) {
          logger.error(`Error parsing item from queue ${queue}:`, error);
        }
      }
      return parsedItems;
    } catch (error) {
      logger.error(`Error fetching items from queue ${queue}:`, error);
      return [];
    }
  }

  protected async getAlarmsFromQueue(
    severity: string,
    count: number,
  ): Promise<EventData[]> {
    const normalizedSeverity = severity.toLowerCase();
    // Step 1: First check the escalation queue for just ONE item (highest priority)
    // If found, return immediately regardless of the requested count
    const escalationItems = await this.getItemsFromQueue(
      this.orphan_escalation_queue,
      1,
    );
    if (escalationItems.length > 0) {
      return escalationItems; // Return just the 1 escalation item
    }
    const results: EventData[] = [];
    const remainingCount = () => count - results.length;

    // Step 2: Check orphan queues based on severity
    if (normalizedSeverity === "high") {
      const highOrphanItems = await this.getItemsFromQueue(
        this.orphan_high_alarm_queue,
        remainingCount(),
      );
      results.push(...highOrphanItems);
    } else {
      const lowOrphanItems = await this.getItemsFromQueue(
        this.orphan_low_alarm_queue,
        remainingCount(),
      );
      results.push(...lowOrphanItems);
    }

    // If we have enough items already, return early
    if (results.length >= count) {
      return results;
    }

    // Step 3: Check the severity-specific queue

    const severityQueue =
      this.severityToQueue[normalizedSeverity] ||
      this.severityToQueue[NOT_KNOWN_SEVERITY];
    const severityItems = await this.getItemsFromQueue(
      severityQueue,
      remainingCount(),
    );
    results.push(...severityItems);

    return results;
  }

  protected async fetchFromQueue(
    severity: string,
    count: number,
  ): Promise<any[]> {
    /**
     * TODO: Check with the team how to fetch alarms for Scan and Monitoring Alarms
     */
    const queues = [
      this.orphan_escalation_queue,
      severity === "high"
        ? this.orphan_high_alarm_queue
        : this.orphan_low_alarm_queue,
      this.severityToQueue[severity],
    ];
    const events: any[] = [];

    for (const queueName of queues) {
      for (let i = 0; i < count; i++) {
        const serializedData = await this.redisClient.lpop(queueName);
        if (serializedData === null) {
          break; // Stop if the queue is empty
        }
        const eventData = JSON.parse(serializedData);

        // If the event is from the orphan_escalation_queue, return it immediately
        if (queueName === this.orphan_escalation_queue) {
          return [eventData];
        }

        events.push(eventData);
      }
      if (events.length >= count) {
        break; // Stop if we have fetched enough events
      }
    }

    return events;
  }

  protected async addAlarmToQueue(
    alarm_data: string,
    queue: string,
  ): Promise<number> {
    return await this.redisClient.lpush(queue, alarm_data);
  }

  // Add one or more alarm IDs to the set for a given operator_id
  protected async addAlarmToOperator(
    operator_id: string,
    alarm_data: EventData,
  ): Promise<void> {
    const operatorKey = `operator_alarms:${operator_id}`;
    const alarmKey = `alarm_operators:${alarm_data.alarm_group_id}`;
    const serializedEvent = JSON.stringify(alarm_data);

    const pipeline = this.redisClient.pipeline();
    // Store the alarm data in a hash
    pipeline.hset(operatorKey, alarm_data.alarm_group_id, serializedEvent);
    // Add the operator to the alarm's set of operators
    pipeline.sadd(alarmKey, operator_id);
    await pipeline.exec();
  }

  /**
   * Updates the operator-alarm mapping in Redis with retry logic and atomic operations
   * @param event Event data containing operator and alarm information
   * @param maxRetries Maximum number of retry attempts (default: 3)
   * @returns Promise<boolean> indicating success/failure
   */
  protected async updateOperatorAlarmMapping(
    event: {
      alarm_group_id: string;
      tenant_id: string;
      camera_group_id: string;
      operator_id: string;
      severity?: string;
      is_escalation?: boolean;
    },
    maxRetries = 3,
  ): Promise<boolean> {
    let retryCount = 0;
    let success = false;

    while (!success && retryCount < maxRetries) {
      try {
        // Check if this is an escalation
        const isEscalation =
          event.is_escalation ??
          (await this.isAlarmEscalation(event.camera_group_id));

        // Prepare event data for Redis
        const eventData = {
          alarm_group_id: event.alarm_group_id,
          tenant_id: event.tenant_id,
          camera_group_id: event.camera_group_id,
          operator_id: event.operator_id,
          severity: event.severity || null,
          is_escalation: isEscalation,
        };

        // Create keys for Redis operations
        const operatorKey = `operator_alarms:${event.operator_id}`;
        const alarmOperatorKey = `alarm_operator:${event.alarm_group_id}`;
        const serializedEvent = JSON.stringify(eventData);

        // Use Redis MULTI for true atomicity
        const multi = this.redisClient.multi();

        // Watch the keys we're going to modify to detect changes
        await this.redisClient.watch(alarmOperatorKey, operatorKey);

        // Check if alarm already has an operator assigned
        const previousOperatorId = await this.redisClient.get(alarmOperatorKey);

        // If a previous operator exists and is different, remove this alarm from their list
        if (previousOperatorId && previousOperatorId !== event.operator_id) {
          multi.hdel(
            `operator_alarms:${previousOperatorId}`,
            event.alarm_group_id,
          );
        }

        // Store the alarm data in the operator's hash
        multi.hset(operatorKey, event.alarm_group_id, serializedEvent);

        // Set the operator as the sole operator for this alarm (1:1 mapping)
        multi.set(alarmOperatorKey, event.operator_id);

        if (isEscalation) {
          multi.hset(
            `operator:${event.operator_id}:escalations`,
            event.camera_group_id,
            serializedEvent,
          );
        }

        // Add expiry commands
        multi.expire(operatorKey, 86400); // 1 day expiry
        multi.expire(alarmOperatorKey, 86400);
        if (isEscalation) {
          multi.expire(`operator:${event.operator_id}:escalations`, 86400);
        }

        // Execute the transaction
        const results = await multi.exec();

        // If results is null, the transaction was aborted due to a watched key changing
        if (results === null) {
          throw new Error("Transaction aborted due to concurrent modification");
        }

        success = true;
        return true;
      } catch (error) {
        retryCount++;
        logger.warn("Redis operation failed, retrying", {
          attempt: retryCount,
          maxRetries,
          error: error instanceof Error ? error.message : String(error),
        });

        if (retryCount >= maxRetries) {
          logger.error("Redis operations failed after retries", {
            error: error instanceof Error ? error.message : String(error),
          });
          return false;
        }

        // Add exponential backoff delay between retries
        await new Promise((resolve) =>
          setTimeout(resolve, 2 ** retryCount * 100),
        );
      }
    }

    return false;
  }

  // Retrieve the list of alarm IDs for a given operator_id
  protected async getAlarmsForOperator(
    operator_id: string,
  ): Promise<EventData[]> {
    const operatorKey = `operator_alarms:${operator_id}`;
    const alarms = await this.redisClient.hgetall(operatorKey);

    return Object.values(alarms).map((serializedAlarm) =>
      JSON.parse(serializedAlarm),
    );
  }

  protected async removeOperatorForAlarm(alarm_id: string): Promise<void> {
    const key = `alarm:${alarm_id}:operator`;
    const pipeline = this.redisClient.pipeline();
    pipeline.del(key);
    await pipeline.exec();
  }

  protected async removeAlarmFromOperator(
    operator_id: string,
    alarm_group_id: string,
  ): Promise<void> {
    const setKey = `operator:${operator_id}:alarms`;
    await this.redisClient.hdel(setKey, alarm_group_id);
  }

  // Remove all alarms for a given operator_id
  protected async removeAllAlarmsForOperator(
    operator_id: string,
  ): Promise<number> {
    const operatorKey = `operator_alarms:${operator_id}`;
    // Get all alarm_group_ids before deleting
    const alarmIds = await this.redisClient.hkeys(operatorKey);

    const pipeline = this.redisClient.pipeline();
    // Delete the operator's hash
    pipeline.del(operatorKey);

    // Remove operator from each alarm's set
    for (const alarmId of alarmIds) {
      pipeline.srem(`alarm_operators:${alarmId}`, operator_id);
    }

    await pipeline.exec();
    return alarmIds.length;
  }

  protected async getEscalationsForOperator(
    operator_id: string,
  ): Promise<EventData[]> {
    const setKey = `operator:${operator_id}:escalations`;
    const eventDataDict = await this.redisClient.hgetall(setKey);
    const eventList: EventData[] = Object.values(eventDataDict).map(
      (jsonString) => JSON.parse(jsonString),
    );
    return eventList;
  }

  protected async removeAllEscalationsForOperator(
    operator_id: string,
  ): Promise<number> {
    const setKey = `operator:${operator_id}:escalations`;
    return await this.redisClient.del(setKey);
  }

  protected async setOperatorForAlarm(
    alarm_id: string,
    operator_id: string,
  ): Promise<void> {
    const key = `alarm:${alarm_id}:operator`;
    const pipeline = this.redisClient.pipeline();
    pipeline.set(key, operator_id, "EX", this.expirySeconds.one_day);
    await pipeline.exec();
  }

  protected async getOperatorForAlarmId(
    alarm_id: string,
  ): Promise<string | null> {
    const key = `alarm_operator:${alarm_id}`;
    return await this.redisClient.get(key);
  }

  protected async updateFramesWithSignedUrls(
    event: DetectionEvent,
  ): Promise<DetectionEvent> {
    const FRAMES_KEY = "frames";
    try {
      if (event.metadata?.[FRAMES_KEY]) {
        // Assuming frames is an array of [s3url, timestamp] tuples
        const updatedFrames = await Promise.all(
          event.metadata[FRAMES_KEY].map(
            async ([s3url, timestamp]: [string, string]) => {
              try {
                const signedUrl = await this.generateSignedUrl(s3url);
                return [signedUrl, timestamp];
              } catch (frameError) {
                // Log the error but continue processing other frames
                logger.error(`Failed to process frame with URL: ${s3url}`, {
                  error:
                    frameError instanceof Error
                      ? frameError.message
                      : String(frameError),
                });
                // Return the original URL and timestamp as fallback
                return [s3url, timestamp];
              }
            },
          ),
        );

        return {
          ...event,
          metadata: {
            ...event.metadata,
            frames: updatedFrames,
          },
        };
      }
      return event; // Return original event if no frames exist
    } catch (error) {
      // Log the error but don't crash the server
      logger.error("Failed to update frames with signed URLs", {
        error: error instanceof Error ? error.message : String(error),
        eventId: event.id,
      });

      // Return the original event as a fallback
      return event;
    }
  }

  private async generateSignedUrl(s3url: string): Promise<string> {
    try {
      const s3Client = new S3Client({ region: config.s3Config.region });
      const command = new GetObjectCommand({
        Bucket: config.s3Config.bucketName,
        Key: this.extractKeyFromUrl(s3url),
      });
      return await getSignedUrl(s3Client, command, { expiresIn: 3600 });
    } catch (error) {
      // Log the error but don't crash the server
      logger.error(`Failed to generate signed URL for S3 object: ${s3url}`, {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Return the original URL as a fallback
      // This allows the application to continue functioning even if signed URLs fail
      return s3url;
    }
  }

  private extractKeyFromUrl(s3url: string): string {
    try {
      const url = new URL(s3url);
      // Everything after the domain name is our key
      return decodeURIComponent(url.pathname.slice(1));
    } catch (error) {
      logger.error(`Failed to extract key from S3 URL: ${s3url}`, error);
      throw new Error(`Invalid S3 URL format: ${s3url}`);
    }
  }

  protected async fetchUnifiedAlarms(
    cameraIds: string[],
    locationId: string,
    beginTime: number,
    endTime: number,
    statuses: string,
    authToken: string,
  ): Promise<GatewayResponse<UnifiedAlarm[]>> {
    try {
      const searchParams = this.getAlarmsSearchParams(
        cameraIds,
        locationId,
        beginTime,
        endTime,
        statuses === "All" ? [] : ([statuses] as AlarmStatus[]),
      );

      const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/alarm?${searchParams}`;
      const response = await fetch(gatewayUrl, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      if (!response.ok) {
        logger.error("Failed to fetch unified alarms:", response.statusText);
        return { success: false, error: response.statusText };
      }

      const data = await response.json();
      const unifiedAlarms = await Promise.all(
        data.payload.items.map((alarm) =>
          this.marshalToUnifiedAlarm(alarm, authToken),
        ),
      );
      return { success: true, data: unifiedAlarms };
    } catch (error) {
      logger.error("Error fetching pending location alarms:", error);
      return { success: false };
    }
  }

  private async marshalToUnifiedAlarm(
    alarm: any,
    authToken: string,
  ): Promise<UnifiedAlarm> {
    const alarmMedia = await this.fetchAlarmMedia(
      alarm.id,
      alarm.timestamp,
      authToken,
    );
    const isTalkDownEnabled = Boolean(alarm.sourceEntity.audio_devices?.length);

    return {
      id: alarm.id,
      alarmGroupId: alarm.id,
      timestamp: alarm.timestamp,
      cameraId: alarm.sourceEntity.id,
      cameraName: alarm.sourceEntity.name || "",
      cameraGroupId: "",
      cameraGroupName: "",
      cameraLiveStreamUrl: alarm.sourceEntity.livestreamUrl || "",
      cameraPlayBackUrl: "",
      alarmStartTime: alarm.videoStartTime
        ? new Date(alarm.videoStartTime).getTime()
        : Date.now(),
      alarmEndTime: alarm.videoEndTime
        ? new Date(alarm.videoEndTime).getTime()
        : Date.now(),
      severity: "",
      media: {
        type: "video",
        video: {
          url: alarmMedia?.videoPath || "",
          boxes: alarmMedia?.boxes || [],
          startTime: alarm.videoStartTime || "",
          endTime: alarm.videoEndTime || "",
        },
        frames: [], // Frames need to be fetched separately
      },
      resolvedAlarm: alarm.status === "Resolved by Hakimo",
      isTalkDownEnabled,
    };
  }

  protected async fetchUnifiedAlarm(
    alarmId: string,
    authToken: string,
  ): Promise<GatewayResponse<UnifiedAlarmGroup>> {
    try {
      const locationAlarmResp = await this.fetchLocationAlarm(
        alarmId,
        authToken,
      );
      if (!locationAlarmResp.success) {
        return { success: false, error: locationAlarmResp.error };
      }
      const locationAlarm = locationAlarmResp.data;
      if (!locationAlarm) {
        return { success: false, error: "Location alarm doesn't exist" };
      }
      const locationAlarmTimestamp = new Date(
        locationAlarm.alarmTime,
      ).toISOString();
      const alarmMediaMap = new Map<string, AlarmMediaResponse | undefined>();
      const alarmIds = locationAlarm.rawAlarms.map((alarm) => alarm.id);
      const uniqueAlarmIds = [...new Set(alarmIds)];
      const alarmsMedia = await this.fetchAlarmsMedia(
        uniqueAlarmIds,
        authToken,
        locationAlarmTimestamp,
      );

      for (const media of alarmsMedia) {
        alarmMediaMap.set(media.alarmId, media);
      }
      const unifiedAlarmGroup = await this.marshalToUnifiedAlarmGroup(
        locationAlarm,
        alarmMediaMap,
      );
      return { success: true, data: unifiedAlarmGroup };
    } catch (error) {
      logger.error("Error fetching unified alarm details", { error, alarmId });
      return { success: false };
    }
  }

  private getAlarmsSearchParams(
    cameraIds: string[],
    locationId: string,
    beginTime: number,
    endTime: number,
    statuses: AlarmStatus[],
  ): string {
    const SEPARATOR = "^^^";
    const params = new URLSearchParams();
    const date = new Date(beginTime);
    const partitioKey = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, "0")}`;
    if (cameraIds && cameraIds.length > 0) {
      const query = cameraIds.join(SEPARATOR);
      params.append("sourceEntityIds", query);
    }
    locationId && params.append("locationIds", locationId);
    beginTime && params.append("dateFrom", String(beginTime));
    endTime && params.append("dateTo", String(endTime));
    if (statuses && statuses.length > 0) {
      const query = statuses.join(SEPARATOR);
      params.append("statuses", query);
    }
    params.append("partitionKey", partitioKey);
    return params.toString();
  }

  private async fetchResolvedMotionAlarms(
    rawAlarms: MotionAlarm[],
    locationId: string,
    authToken: string,
  ): Promise<AlarmsDTO> {
    const startTime = performance.now();
    try {
      const cameraIds = [
        ...new Set(rawAlarms.map((alarm) => alarm.sourceEntity.id)),
      ];
      const videoTimestamps: number[] = [];
      for (const alarm of rawAlarms) {
        if (alarm.videoStartTime) {
          videoTimestamps.push(new Date(alarm.videoStartTime).getTime());
        }
        if (alarm.videoEndTime) {
          videoTimestamps.push(new Date(alarm.videoEndTime).getTime());
        }
      }

      videoTimestamps.sort();
      const beginTime =
        videoTimestamps.length > 0 ? videoTimestamps[0] : Date.now();
      const endTime =
        videoTimestamps.length > 0
          ? videoTimestamps[videoTimestamps.length - 1]
          : Date.now();
      const statuses = ["Resolved by Hakimo"] as AlarmStatus[];
      const searchParams = this.getAlarmsSearchParams(
        cameraIds,
        locationId,
        beginTime,
        endTime,
        statuses,
      );
      const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/alarm?${searchParams}`;
      const responseStartTime = performance.now();
      const response = await fetch(gatewayUrl, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
      const responseEndTime = performance.now();

      const fetchDuration = responseEndTime - responseStartTime;
      metrics.profiling.fetchResolvedMotionAlarms.observe(
        { method: "fetch" },
        fetchDuration,
      );
      logger.info(
        `[PROFILE:FETCH_RESOLVED_MOTION_ALARMS] Fetch took ${fetchDuration.toFixed(2)}ms`,
      );

      if (!response.ok) {
        logger.error(
          "Failed to fetch resolved motion alarms:",
          response.statusText,
        );
        return { total: 0, items: [] };
      }
      const data = await response.json();
      return this.transformToAlarmsDTO(data);
    } catch (error) {
      logger.error("Error fetching resolved motion alarms", error);
      return { total: 0, items: [] };
    } finally {
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      metrics.profiling.fetchResolvedMotionAlarms.observe(
        { method: "total" },
        totalDuration,
      );
      logger.info(
        `[PROFILE:FETCH_RESOLVED_MOTION_ALARMS] Total duration: ${totalDuration.toFixed(2)}ms`,
      );
    }
  }

  private transformToAlarmsDTO(apiResponse: any): AlarmsDTO {
    const items: Alarm[] = apiResponse.payload.items.map((item: any) => ({
      id: item.id,
      type: item.type,
      timestamp: item.timestamp,
      status: item.status as AlarmStatus,
      tap: item.tap,
      sourceSystem: item.sourceSystem,
      location: item.location
        ? {
            id: item.location.id,
            name: item.location.name,
            city: item.location.city,
            state: item.location.state,
            country: item.location.country,
            description: item.location.description,
            tenant_id: item.location.tenant_id,
            timezone: item.location.timezone,
          }
        : undefined,
      sourceEntity: item.sourceEntity
        ? {
            id: item.sourceEntity.id,
            name: item.sourceEntity.name,
            type: item.sourceEntity.type as "CAMERA" | "DOOR",
          }
        : undefined,
      tags: item.tags || [],
      tenant: {
        id: item.tenant.id,
        name: item.tenant.name,
      },
      videoStartTime: item.videoStartTime,
      videoEndTime: item.videoEndTime,
    }));

    return {
      total: apiResponse.payload.total,
      items,
    };
  }

  private async fetchAlarmsMedia(
    alarmIds: string[],
    authToken: string,
    locationAlarmTimestamp?: string,
  ): Promise<AlarmMediaResponse[]> {
    // check if data is present in cache
    const alarmsMedia: AlarmMediaResponse[] = [];
    const alarmIdsToFetchMedia = [];

    for (const alarmId of alarmIds) {
      const cacheKey = `${this.alarmMediaCachePrefix}${alarmId}`;
      const cachedAlarmMedia = await this.redisClient.get(cacheKey);
      if (cachedAlarmMedia) {
        const alarmMediaResp = JSON.parse(
          cachedAlarmMedia,
        ) as AlarmMediaResponse;
        if (alarmMediaResp.url) {
          alarmsMedia.push(alarmMediaResp);
        } else {
          alarmIdsToFetchMedia.push(alarmId);
        }
      } else {
        alarmIdsToFetchMedia.push(alarmId);
      }
    }

    // fetch media from gateway
    if (alarmIdsToFetchMedia.length > 0) {
      const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/alarm/media`;

      const requestPayload = {
        alarmIds: alarmIdsToFetchMedia,
        includeBoxes: true,
        timestamp: locationAlarmTimestamp,
      };

      const response = await fetch(gatewayUrl, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestPayload),
      });
      if (!response.ok) {
        logger.error("Failed to fetch alarm media:", response.statusText);
        return [];
      }
      const data = await response.json();

      if (!data.payload) {
        return [];
      }

      const alarmsMediaToCache: AlarmMediaResponse[] = data.payload;
      for (const alarmMedia of alarmsMediaToCache) {
        const mediaUrl = alarmMedia.url;
        if (!mediaUrl) {
          logger.warning(
            "AlarmsMediaLogs:alarm media response from gateway doesn't have url",
            { alarmMedia },
          );
          continue;
        }
        alarmsMedia.push(alarmMedia);
        const cacheKey = `${this.alarmMediaCachePrefix}${alarmMedia.alarmId}`;
        await this.redisClient.set(
          cacheKey,
          JSON.stringify(alarmMedia),
          "EX",
          this.alarmMediaCacheExpirySeconds,
        );
      }
    }

    return alarmsMedia;
  }

  private async fetchAlarmMedia(
    motionAlarmId: string,
    timestamp: number,
    authToken: string,
  ): Promise<AlarmMedia | undefined> {
    // Check if we have a valid cached response in Redis
    const cacheKey = `${this.alarmMediaCachePrefix}${motionAlarmId}`;
    const cachedData = await this.redisClient.get(cacheKey);

    if (cachedData) {
      logger.debug(`Using cached alarm media for alarm ID: ${motionAlarmId}`);
      return JSON.parse(cachedData) as AlarmMedia;
    }
    const date = new Date(timestamp);
    const partitionKey = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, "0")}`;

    const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/alarm/media/${motionAlarmId}?includeBoxes=true&partitionKey=${partitionKey}`;
    const response = await fetch(gatewayUrl, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      logger.error("Failed to fetch alarm media:", response.statusText);
      return undefined;
    }
    const data = await response.json();

    if (!data.payload) {
      return undefined;
    }

    const alarmMedia: AlarmMedia = {
      videoPath: data.payload.videoPath,
      boxes: data.payload.boxes?.map((box: any) => ({
        frame: box.frame,
        coords: box.coords,
        type: box.type as BoxType,
      })),
      videoDetails: data.payload.videoDetails
        ? {
            markers: data.payload.videoDetails.markers.map((marker: any) => ({
              timestamp: marker.timestamp,
              label: marker.label,
              type: marker.type,
            })),
            startTime: data.payload.videoDetails.startTime,
          }
        : undefined,
    };

    // Cache the result in Redis with expiry
    if (alarmMedia) {
      await this.redisClient.set(
        cacheKey,
        JSON.stringify(alarmMedia),
        "EX",
        this.alarmMediaCacheExpirySeconds,
      );
    }

    return alarmMedia;
  }

  private async fetchLocation(
    locationId: string,
    authToken: string,
  ): Promise<Location | undefined> {
    // Not using this for now
    const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/locations/${locationId}`;
    const response = await fetch(gatewayUrl, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      // TODO: Alert of unable to fetch location
      logger.error("Failed to fetch location:", response.statusText);
      return undefined;
    }

    const data = await response.json();
    const payload = data.payload;

    // Map the API response to Location interface
    const location: Location = {
      id: payload.id,
      name: payload.name,
      city: payload.city,
      state: payload.state,
      country: payload.country,
      description: payload.description,
      tenant_id: payload.tenant_id,
      timezone: payload.timezone,
    };

    return location;
  }

  private async fetchLocationAlarm(
    alarmId: string,
    authToken: string,
  ): Promise<GatewayResponse<LocationAlarm>> {
    try {
      const gatewayUrl = `${config.gatewayProtocol}://${config.gatewayHost}/v2/orm/location_alarms/${alarmId}`;
      const response = await fetch(gatewayUrl, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      if (!response.ok) {
        // TODO: Alert of unable to fetch location
        logger.error("Failed to fetch unified alarm:", response.statusText);
        return { success: false, error: response.statusText };
      }

      const data = await response.json();
      const apiPayload = data.payload;

      // Validate the status is a valid LocationAlarmStatus
      let validatedStatus: LocationAlarmStatus;
      if (locationAlarmStatuses.includes(apiPayload.status as any)) {
        validatedStatus = apiPayload.status as LocationAlarmStatus;
      } else {
        logger.warn(
          `Unexpected location alarm status: ${apiPayload.status}, defaulting to "Pending"`,
        );
        validatedStatus = "Pending";
      }

      // Properly map each raw alarm to ensure it matches the MotionAlarm interface
      const typedRawAlarms: MotionAlarm[] = apiPayload.rawAlarms.map(
        (alarm: any) => ({
          id: alarm.id,
          timestamp: alarm.timestamp,
          videoStartTime: alarm.videoStartTime,
          videoEndTime: alarm.videoEndTime,
          sourceEntity: {
            id: alarm.sourceEntity.id,
            name: alarm.sourceEntity.name,
            audio_devices: alarm.sourceEntity.audio_devices?.map(
              (device: any) => ({
                id: device.id,
              }),
            ),
            livestreamUrl: alarm.sourceEntity.livestreamUrl,
          },
          status: alarm.status,
        }),
      );

      const resolvedMotionAlarms = await this.fetchResolvedMotionAlarms(
        typedRawAlarms,
        apiPayload.location.id ?? "",
        authToken,
      );

      // Convert AlarmsDTO to MotionAlarm array
      const resolvedAlarms: MotionAlarm[] = resolvedMotionAlarms.items.map(
        (alarm) => ({
          id: alarm.id,
          timestamp: alarm.timestamp,
          videoStartTime: alarm.videoStartTime,
          videoEndTime: alarm.videoEndTime,
          sourceEntity: {
            id: alarm.sourceEntity?.id || "",
            name: alarm.sourceEntity?.name,
            audio_devices: [], // API doesn't provide this info for resolved alarms
            livestreamUrl: undefined, // API doesn't provide this info for resolved alarms
          },
          status: alarm.status,
        }),
      );

      // Add resolved alarms to typedRawAlarms
      typedRawAlarms.push(...resolvedAlarms);

      // Map location incidents if present
      const typedLocationIncidents: LocationAlarmIncidentDTO[] = [];
      if (
        apiPayload.locationIncidents &&
        Array.isArray(apiPayload.locationIncidents)
      ) {
        for (const incident of apiPayload.locationIncidents) {
          typedLocationIncidents.push({
            id: incident.id,
            location_id: incident.location_id,
            tenant_id: incident.tenant_id,
            title: incident.title,
            description: incident.description,
            created_by: incident.created_by,
            resolved_by: incident.resolved_by,
            incidentStartTimeUtc: incident.incidentStartTimeUtc,
            incidentEndTimeUtc: incident.incidentEndTimeUtc,
            initiatingLocationAlarmId: incident.initiatingLocationAlarmId,
          });
        }
      }

      // Transform the API response to match LocationAlarm interface
      const locationAlarm: LocationAlarm = {
        id: String(apiPayload.id),
        guid: apiPayload.guid,
        alarmTime: apiPayload.alarmTime,
        status: validatedStatus,
        location: {
          id: apiPayload.location.id,
          name: apiPayload.location.name,
          city: apiPayload.location.city,
          state: apiPayload.location.state,
          country: apiPayload.location.country,
          description: apiPayload.location.description,
          tenant_id: apiPayload.location.tenant_id,
          timezone: apiPayload.location.timezone,
        },
        tap: apiPayload.tap,
        rawAlarms: typedRawAlarms,
        locationIncidents: typedLocationIncidents,
      };

      return { success: true, data: locationAlarm };
    } catch (error) {
      logger.error("Error fetching location alarm detail", { error, alarmId });
      return { success: false };
    }
  }

  protected async marshalToUnifiedAlarmGroup(
    locationAlarm: LocationAlarm,
    alarmMediaMap: Map<string, AlarmMediaResponse | undefined>,
  ): Promise<UnifiedAlarmGroup> {
    // Transform raw alarms to UnifiedAlarm format
    const unifiedAlarms: UnifiedAlarm[] = locationAlarm.rawAlarms.map(
      (alarm) => {
        const alarmMedia = alarmMediaMap.get(alarm.id);
        const isTalkDownEnabled = Boolean(
          alarm.sourceEntity.audio_devices?.length,
        );

        return {
          id: alarm.id,
          alarmGroupId: locationAlarm.id,
          timestamp: alarm.timestamp,
          cameraId: alarm.sourceEntity.id,
          cameraName: alarm.sourceEntity.name || "",
          cameraGroupId: "", // Not available in source data
          cameraGroupName: "", // Not available in source data
          cameraLiveStreamUrl: alarm.sourceEntity.livestreamUrl || "",
          cameraPlayBackUrl: "", // Not available in source data
          alarmStartTime: alarm.videoStartTime
            ? new Date(alarm.videoStartTime).getTime()
            : Date.now(),
          alarmEndTime: alarm.videoEndTime
            ? new Date(alarm.videoEndTime).getTime()
            : Date.now(),
          severity: "", // Not available in source data
          media: {
            type: "video",
            video: {
              url: alarmMedia?.url || "",
              boxes: alarmMedia?.boxes || [],
              startTime: alarm.videoStartTime || "",
              endTime: alarm.videoEndTime || "",
            },
            frames: [], // Frames need to be fetched separately
          },
          resolvedAlarm: alarm.status === "Resolved by Hakimo",
          isTalkDownEnabled,
        };
      },
    );

    // Transform location incidents to Escalation format
    const escalations: Escalation[] = (
      locationAlarm.locationIncidents || []
    ).map((incident) => ({
      id: String(incident.id),
      title: incident.title || "",
      description: incident.description || "",
      incidentStartTimeUtc: incident.incidentStartTimeUtc || "",
      incidentEndTimeUtc: incident.incidentEndTimeUtc || "",
      initiatingLocationAlarmId: String(incident.initiatingLocationAlarmId),
    }));

    // Create a UnifiedLocation object from the locationAlarm.location
    const unifiedLocation: UnifiedLocation = {
      id: locationAlarm.location.id,
      name: locationAlarm.location.name,
      city: locationAlarm.location.city,
      state: locationAlarm.location.state,
      country: locationAlarm.location.country,
      description: locationAlarm.location.description,
      tenant_id: locationAlarm.location.tenant_id,
      timezone: locationAlarm.location.timezone,
    };

    return {
      id: locationAlarm.id,
      timestamp: locationAlarm.alarmTime,
      tenantId: locationAlarm.location.tenant_id,
      location: unifiedLocation,
      alarms: unifiedAlarms,
      alarmCount: unifiedAlarms.length,
      status: locationAlarm.status,
      severity: "",
      escalations: escalations,
      product: Product.REMOTE_GUARDING,
      tapScore: locationAlarm.tap,
    };
  }

  /**
   * Filter queues by severity level
   */
  private filterQueuesBySeverity(queues: string[], severity: string): string[] {
    const normalizedSeverity = severity.toLowerCase();
    return queues.filter((q) =>
      q.includes(`_${normalizedSeverity}_severity_queue`),
    );
  }

  private filterQueuesByVisionTenantIds(
    queues: string[],
    vision_tenant_ids: string[],
  ): string[] {
    if (!vision_tenant_ids.length) return []; // If no tenant IDs provided, return empty array

    return queues.filter((queue) => {
      return vision_tenant_ids.some((tenantId) => {
        // Check if queue contains any of the tenant ID patterns
        return (
          queue.includes(`tenant_${tenantId}_`) ||
          queue.includes(`orphan_tenant_${tenantId}_`) ||
          queue.includes(`orphan_escalation_${tenantId}_`)
        );
      });
    });
  }

  protected async getPendingEvents(
    operator_id: string,
    count: number,
    vision_tenant_ids: string[] = [],
    severity?: string,
  ): Promise<string | null> {
    logger.info(`Getting pending events for operator: ${operator_id}`);
    const activeQueues = await this.redisClient.smembers("active_queues");
    logger.info(`Active queues: ${activeQueues}`);
    const operatorActiveQueues = this.filterQueuesByVisionTenantIds(
      activeQueues,
      vision_tenant_ids,
    );
    logger.info(`Operator active queues: ${operatorActiveQueues}`);
    const orphanQueues = await this.redisClient.smembers("orphan_queues");
    const operatorOrphanQueues = this.filterQueuesByVisionTenantIds(
      orphanQueues,
      vision_tenant_ids,
    );
    logger.info(`Operator orphan queues: ${operatorOrphanQueues}`);
    const orphanEscalationQueues = await this.redisClient.smembers(
      "orphan_escalation_queues",
    );
    logger.info(`Orphan escalation queues: ${orphanEscalationQueues}`);
    const operatorOrphanEscalationQueues = this.filterQueuesByVisionTenantIds(
      orphanEscalationQueues,
      vision_tenant_ids,
    );
    logger.info(
      `Operator orphan escalation queues: ${operatorOrphanEscalationQueues}`,
    );

    const shuffledActiveQueues = this.shuffleArray(operatorActiveQueues);
    const shuffledOrphanQueues = this.shuffleArray(operatorOrphanQueues);
    const shuffledOrphanEscalationQueues = this.shuffleArray(
      operatorOrphanEscalationQueues,
    );
    const processingQueue = `processing_queue:${operator_id}`;
    let processedCount = await this.redisClient.llen(processingQueue);
    // 2. First handle escalation queues with highest priority
    if (shuffledOrphanEscalationQueues.length > 0 && count > processedCount) {
      await this.fetchEventsFromQueues(
        shuffledOrphanEscalationQueues,
        count - processedCount,
        operator_id,
      );
    }
    processedCount = await this.redisClient.llen(processingQueue);
    if (processedCount >= count) {
      return processingQueue;
    }
    // 3. Handle as per severity
    if (severity) {
      const normalizedSeverity = severity.toLowerCase();
      const orphanSeverityQueues = this.filterQueuesBySeverity(
        shuffledOrphanQueues,
        normalizedSeverity,
      );
      if (orphanSeverityQueues.length > 0 && count - processedCount > 0) {
        await this.fetchEventsFromQueues(
          orphanSeverityQueues,
          count - processedCount,
          operator_id,
        );
      }
      processedCount = await this.redisClient.llen(processingQueue);
      if (processedCount >= count) {
        return processingQueue;
      }
      const queues = this.filterQueuesBySeverity(
        shuffledActiveQueues,
        normalizedSeverity,
      );
      if (queues.length > 0 && count - processedCount > 0) {
        await this.fetchEventsFromQueues(
          queues,
          count - processedCount,
          operator_id,
        );
      }
    } else {
      // 4. Handle as per generic case
      const remainingCount = count - processedCount;
      const highTargetCount = Math.ceil(remainingCount / 2);
      const lowTargetCount = remainingCount - highTargetCount;
      const highOrphanQueues = this.filterQueuesBySeverity(
        shuffledOrphanQueues,
        "high",
      );
      const highActiveQueues = this.filterQueuesBySeverity(
        shuffledActiveQueues,
        "high",
      );
      const allHighQueues = [...highOrphanQueues, ...highActiveQueues];
      logger.info(`All high queues: ${allHighQueues}`);
      if (allHighQueues.length > 0 && highTargetCount > 0) {
        await this.fetchEventsFromQueues(
          allHighQueues,
          highTargetCount,
          operator_id,
        );
      }
      processedCount = await this.redisClient.llen(processingQueue);
      if (processedCount >= count) {
        return processingQueue;
      }
      const lowOrphanQueues = this.filterQueuesBySeverity(
        shuffledOrphanQueues,
        "low",
      );
      const lowActiveQueues = this.filterQueuesBySeverity(
        shuffledActiveQueues,
        "low",
      );
      const allLowQueues = [...lowOrphanQueues, ...lowActiveQueues];
      logger.info(`All low queues: ${allLowQueues}`);
      if (allLowQueues.length > 0 && lowTargetCount > 0) {
        await this.fetchEventsFromQueues(
          allLowQueues,
          lowTargetCount,
          operator_id,
        );
      }
      processedCount = await this.redisClient.llen(processingQueue);
      if (processedCount >= count) {
        return processingQueue;
      }
      const remainingQueues = count - processedCount;
      const combinedAllQueues = [...allHighQueues, ...allLowQueues];
      await this.fetchEventsFromQueues(
        combinedAllQueues,
        remainingQueues,
        operator_id,
      );
    }
    return processingQueue;
  }

  protected async fetchEventsFromQueues(
    queues: string[],
    count: number,
    operator_id: string,
  ): Promise<void> {
    const processingQueue = `processing_queue:${operator_id}`;
    if (queues.length === 0 || count <= 0) {
      return;
    }

    // Track queues that still have items
    const activeQueues = new Set<string>();
    for (const queue of queues) {
      const queueLength = await this.redisClient.llen(queue);
      if (queueLength > 0) {
        activeQueues.add(queue);
      }
      metrics.product.queueLength.set({ queue_name: queue }, queueLength);
    }

    if (activeQueues.size === 0) {
      return;
    }

    let itemsMoved = 0;
    let currentQueueIndex = 0;
    const queueArray = Array.from(activeQueues);

    while (itemsMoved < count && activeQueues.size > 0) {
      const queue = queueArray[currentQueueIndex];

      // Skip if this queue is no longer active
      if (!activeQueues.has(queue)) {
        currentQueueIndex = (currentQueueIndex + 1) % queueArray.length;
        continue;
      }

      const item = await this.redisClient.lmove(
        queue,
        processingQueue,
        "RIGHT",
        "LEFT",
      );

      if (item) {
        itemsMoved++;
        logger.info(`Moved item: ${item}`);
      }

      // Check if queue is now empty
      const currentQueueLength = await this.redisClient.llen(queue);
      if (currentQueueLength === 0) {
        logger.info(`Queue emptied: ${queue}`);
        activeQueues.delete(queue);
      }
      metrics.product.queueLength.set(
        { queue_name: queue },
        currentQueueLength,
      );

      // Move to next queue
      currentQueueIndex = (currentQueueIndex + 1) % queueArray.length;
    }
  }

  /**
   * Fetch events from queues using a configurable load balancing approach
   * @param count Number of events to fetch
   * @param operator_id Operator ID to associate with the processing queue
   * @param severity Optional severity level to filter queues ("high" or "low")
   * @param loadBalancingStrategy Strategy to use for distributing requests across queues ("roundRobin", "weightedRoundRobin", "consistentHashing")
   * @returns The name of the processing queue for the operator
   */
  protected async getEventsUsingLoadBalancer(
    count: number,
    operator_id: string,
    severity?: string,
    loadBalancingStrategy:
      | "roundRobin"
      | "weightedRoundRobin"
      | "consistentHashing" = "roundRobin",
  ): Promise<string> {
    // 1. Get the active queues from Redis
    const activeQueues = await this.redisClient.smembers("active_queues");
    const orphanQueues = await this.redisClient.smembers("orphan_queues");
    const orphanEscalationQueues = await this.redisClient.smembers(
      "orphan_escalation_queues",
    );

    let processingQueue = "";
    const processedCount = 0;
    const remainingCount = () => count - processedCount;

    // 2. First handle escalation queues with highest priority
    if (orphanEscalationQueues.length > 0 && remainingCount() > 0) {
      const result = await this.fetchEventsFromQueuesWithLoadBalancing(
        orphanEscalationQueues,
        remainingCount(),
        loadBalancingStrategy,
        operator_id,
        true, // This is an escalation queue
      );

      if (result.queue) {
        processingQueue = result.queue;
        // Continue to next queue type instead of returning early
      }
    }

    // 3. Handle orphan and active queues based on severity
    if (severity) {
      // If a specific severity is provided, respect that
      const normalizedSeverity = severity.toLowerCase();

      // First process orphan queues of the specified severity
      const orphanSeverityQueues = this.filterQueuesBySeverity(
        orphanQueues,
        normalizedSeverity,
      );
      if (orphanSeverityQueues.length > 0 && remainingCount() > 0) {
        const result = await this.fetchEventsFromQueuesWithLoadBalancing(
          orphanSeverityQueues,
          remainingCount(),
          loadBalancingStrategy,
          operator_id,
          false, // This is not an escalation queue
        );

        if (result.queue && processingQueue === "") {
          processingQueue = result.queue;
        }
      }

      // Then process active queues of the specified severity if we need more events
      if (remainingCount() > 0) {
        const activeSeverityQueues = this.filterQueuesBySeverity(
          activeQueues,
          normalizedSeverity,
        );
        if (activeSeverityQueues.length > 0) {
          const result = await this.fetchEventsFromQueuesWithLoadBalancing(
            activeSeverityQueues,
            remainingCount(),
            loadBalancingStrategy,
            operator_id,
            false, // This is not an escalation queue
          );

          if (result.queue && processingQueue === "") {
            processingQueue = result.queue;
          }
        }
      }
    } else {
      // Track how many total events we've collected
      let totalEventsCollected = await this.redisClient.llen(processingQueue);
      // Use the balanced approach (50% high, 50% low, favoring high)
      const totalTargetCount = count - totalEventsCollected;
      const highTargetCount = Math.ceil(totalTargetCount / 2);
      const lowTargetCount = totalTargetCount - highTargetCount;

      // First process orphan queues with high severity
      const orphanHighQueues = this.filterQueuesBySeverity(
        orphanQueues,
        "high",
      );
      const totalHighFromBeforeOrphanQueues = totalEventsCollected;
      let remainingHighTarget = highTargetCount;
      if (orphanHighQueues.length > 0 && highTargetCount > 0) {
        const result = await this.fetchEventsFromQueuesWithLoadBalancing(
          orphanHighQueues,
          highTargetCount,
          loadBalancingStrategy,
          operator_id,
          false, // This is not an escalation queue
        );

        if (result.queue) {
          processingQueue = result.queue;
          // Get how many events we collected
          totalEventsCollected = await this.redisClient.llen(processingQueue);
          remainingHighTarget =
            highTargetCount -
            (totalEventsCollected - totalHighFromBeforeOrphanQueues);
        }
      }

      // Then process active queues with high severity if we need more

      if (remainingHighTarget > 0) {
        const activeHighQueues = this.filterQueuesBySeverity(
          activeQueues,
          "high",
        );
        if (activeHighQueues.length > 0) {
          const result = await this.fetchEventsFromQueuesWithLoadBalancing(
            activeHighQueues,
            remainingHighTarget,
            loadBalancingStrategy,
            operator_id,
            false, // This is not an escalation queue
          );

          if (result.queue) {
            if (processingQueue === "") {
              processingQueue = result.queue;
              totalEventsCollected =
                await this.redisClient.llen(processingQueue);
            } else {
              // If we already have a processing queue, we've added more events to it
              totalEventsCollected =
                await this.redisClient.llen(processingQueue);
            }
          }
        }
      }

      // Then process orphan queues with low severity
      const remainingCount = count - totalEventsCollected;
      if (remainingCount > 0) {
        const orphanLowQueues = this.filterQueuesBySeverity(
          orphanQueues,
          "low",
        );
        if (orphanLowQueues.length > 0) {
          const result = await this.fetchEventsFromQueuesWithLoadBalancing(
            orphanLowQueues,
            remainingCount,
            loadBalancingStrategy,
            operator_id,
            false, // This is not an escalation queue
          );

          if (result.queue) {
            if (processingQueue === "") {
              processingQueue = result.queue;
              totalEventsCollected =
                await this.redisClient.llen(processingQueue);
            } else {
              // If we already have a processing queue, we've added more events to it
              totalEventsCollected =
                await this.redisClient.llen(processingQueue);
            }
          }
        }
      }

      // Finally process active queues with low severity
      const finalRemainingCount = count - totalEventsCollected;
      if (finalRemainingCount > 0) {
        const activeLowQueues = this.filterQueuesBySeverity(
          activeQueues,
          "low",
        );
        if (activeLowQueues.length > 0) {
          const result = await this.fetchEventsFromQueuesWithLoadBalancing(
            activeLowQueues,
            finalRemainingCount,
            loadBalancingStrategy,
            operator_id,
            false, // This is not an escalation queue
          );

          if (result.queue) {
            if (processingQueue === "") {
              processingQueue = result.queue;
            } else {
              // We've already added more events to the existing queue
              // No need to update totalEventsCollected here as this is the last step
            }
          }
        }
      }
    }

    // Return the processing queue name if we found any events, otherwise empty string
    return processingQueue;
  }

  /**
   * Fetch events from queues using a configurable load balancing approach
   * @param queues List of queue names to fetch from
   * @param count Number of events to fetch
   * @param loadBalancingStrategy Strategy to use for distributing requests across queues
   * @param operator_id Operator ID to associate with the processing queue
   * @param isEscalationQueue Whether this is an escalation queue
   * @returns The name of the processing queue for the operator
   */
  protected async fetchEventsFromQueuesWithLoadBalancing(
    queues: string[],
    count: number,
    loadBalancingStrategy:
      | "roundRobin"
      | "weightedRoundRobin"
      | "consistentHashing",
    operator_id: string,
    isEscalationQueue: boolean,
  ): Promise<{ queue: string }> {
    if (queues.length === 0 || count <= 0) {
      return { queue: "" };
    }

    const processingQueue = `processing_queue:${operator_id}`;
    let itemsMoved = 0;
    let attempts = 0;
    const maxAttempts = count * queues.length * 2; // Prevent infinite loops, allowing multiple passes through queues

    // For consistent hashing, we'll use the operator_id to determine which queue to use
    if (loadBalancingStrategy === "consistentHashing") {
      // Simple hash function to determine which queue to use
      const hash = this.hashString(operator_id);
      const queueIndex = hash % queues.length;

      // Try to get items from the determined queue first
      while (itemsMoved < count && attempts < maxAttempts) {
        const queue = queues[queueIndex];
        const item = await this.redisClient.lmove(
          queue,
          processingQueue,
          "RIGHT",
          "LEFT",
        );

        if (item) {
          itemsMoved++;
        }

        attempts++;

        // If we've tried this queue multiple times and can't get more items, try other queues
        if (attempts % 5 === 0 && itemsMoved < count) {
          // Try other queues in round-robin fashion
          for (let i = 0; i < queues.length && itemsMoved < count; i++) {
            if (i === queueIndex) continue; // Skip the original queue

            const altQueue = queues[i];
            const altItem = await this.redisClient.lmove(
              altQueue,
              processingQueue,
              "RIGHT",
              "LEFT",
            );

            if (altItem) {
              itemsMoved++;
            }
          }
        }

        // If we've tried all queues multiple times and still don't have enough items, break
        if (
          attempts >= queues.length * 5 &&
          itemsMoved < count &&
          itemsMoved > 0
        ) {
          break;
        }
      }
    }
    // For weighted round robin, we'll use a simple implementation where we try each queue twice
    else if (loadBalancingStrategy === "weightedRoundRobin") {
      let currentQueueIndex = 0;

      while (itemsMoved < count && attempts < maxAttempts) {
        const queue = queues[currentQueueIndex];
        const item = await this.redisClient.lmove(
          queue,
          processingQueue,
          "RIGHT",
          "LEFT",
        );

        if (item) {
          itemsMoved++;
        }

        // Move to the next queue
        currentQueueIndex = (currentQueueIndex + 1) % queues.length;
        attempts++;

        // If we've made several complete passes through all queues but couldn't get count items,
        // and we have at least one item, break to avoid excessive attempts
        if (
          attempts >= queues.length * 3 &&
          itemsMoved < count &&
          itemsMoved > 0
        ) {
          break;
        }
      }
    }
    // Default to round robin
    else {
      let currentQueueIndex = 0;

      while (itemsMoved < count && attempts < maxAttempts) {
        const queue = queues[currentQueueIndex];
        const item = await this.redisClient.lmove(
          queue,
          processingQueue,
          "RIGHT",
          "LEFT",
        );

        if (item) {
          itemsMoved++;
        }

        // Move to the next queue
        currentQueueIndex = (currentQueueIndex + 1) % queues.length;
        attempts++;

        // If we've made several complete passes through all queues but couldn't get count items,
        // and we have at least one item, break to avoid excessive attempts
        if (
          attempts >= queues.length * 3 &&
          itemsMoved < count &&
          itemsMoved > 0
        ) {
          break;
        }
      }
    }

    // Return the processing queue if we moved any items, otherwise empty string
    return {
      queue: itemsMoved > 0 ? processingQueue : "",
    };
  }

  /**
   * Simple hash function for consistent hashing
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  // Abstract method to be implemented by subclasses
  abstract handle(payload: any): Promise<void>;

  protected async removeAlarmIdForOperator(
    operator_id: string,
    alarm_id: string,
  ): Promise<void> {
    const setKey = `operators:alarms:${operator_id}`;
    await this.redisClient.srem(setKey, alarm_id);
  }

  /**
   * Checks if an alarm_group_id was raised as an escalation
   * @param alarm_group_id The alarm group ID to check
   * @returns Promise<boolean> True if the alarm was raised as an escalation, false otherwise
   */
  protected async isAlarmEscalation(camera_group_id: string): Promise<boolean> {
    return (
      (await this.redisClient.hexists(
        "global:escalations",
        camera_group_id,
      )) === 1
    );
  }

  /**
   * Gets all alarm_group_ids that are currently escalated
   * @returns Promise<string[]> Array of alarm_group_ids that are escalated
   */
  protected async getAllEscalatedAlarms(): Promise<string[]> {
    return await this.redisClient.smembers("global:escalations");
  }

  /**
   * Retrieves the AI-generated summary and recommendation for a specific alarm group
   * @param alarm_group_id The ID of the alarm group to retrieve summary for
   * @returns Promise with summary and recommendation, or null if not found
   */
  protected async getAlarmSummaryAndRecommendation(
    alarm_group_id: string,
  ): Promise<{ summary: string; recommendation: string } | null> {
    try {
      const key = `ai_operator_summary:${alarm_group_id}`;
      const result = await this.redisClient.hgetall(key);

      if (!result || Object.keys(result).length === 0) {
        logger.debug(`No AI summary found for alarm group: ${alarm_group_id}`);
        return null;
      }

      return {
        summary: (result as any).summary || "",
        recommendation: (result as any).recommendation || "",
      };
    } catch (error) {
      logger.error("Error retrieving AI summary and recommendation:", {
        error: error instanceof Error ? error.message : String(error),
        alarm_group_id,
      });
      return null;
    }
  }

  /**
   * Implements the Fisher-Yates (Knuth) shuffle algorithm for
   * properly randomizing an array with uniform distribution
   * @param array The array to shuffle
   * @returns A new shuffled array
   */
  private shuffleArray<T>(array: T[]): T[] {
    // Create a copy of the original array to avoid modifying it
    if (!array.length) return [];
    const shuffled = [...array];

    // Fisher-Yates algorithm
    for (let i = shuffled.length - 1; i > 0; i--) {
      // Pick a random index from 0 to i
      const j = Math.floor(Math.random() * (i + 1));
      // Swap elements at indices i and j
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    return shuffled;
  }
}

/**
 * Updates the operator-alarm mapping in Redis with retry logic and atomic operations
 * @param redisClient Redis client instance
 * @param event Event data containing operator and alarm information
 * @param maxRetries Maximum number of retry attempts (default: 3)
 * @returns Promise<boolean> indicating success/failure
 */
async function updateOperatorAlarmMapping(
  redisClient: Redis,
  event: {
    alarm_group_id: string;
    tenant_id: string;
    camera_group_id: string;
    operator_id: string;
    severity?: string;
  },
  maxRetries = 3,
): Promise<boolean> {
  let retryCount = 0;
  let success = false;

  while (!success && retryCount < maxRetries) {
    try {
      // Check if this is an escalation
      const isEscalation =
        (await redisClient.sismember(
          "global:escalations",
          event.alarm_group_id,
        )) === 1;

      // Prepare event data for Redis
      const eventData = {
        alarm_group_id: event.alarm_group_id,
        tenant_id: event.tenant_id,
        camera_group_id: event.camera_group_id,
        operator_id: event.operator_id,
        severity: event.severity || null,
        is_escalation: isEscalation,
      };

      // Create keys for Redis operations
      const operatorKey = `operator_alarms:${event.operator_id}`;
      const alarmOperatorKey = `alarm_operator:${event.alarm_group_id}`;
      const serializedEvent = JSON.stringify(eventData);

      // Use Redis MULTI for true atomicity
      const multi = redisClient.multi();

      // Watch the keys we're going to modify to detect changes
      await redisClient.watch(alarmOperatorKey, operatorKey);

      // Check if alarm already has an operator assigned
      const previousOperatorId = await redisClient.get(alarmOperatorKey);

      // If a previous operator exists and is different, remove this alarm from their list
      if (previousOperatorId && previousOperatorId !== event.operator_id) {
        multi.hdel(
          `operator_alarms:${previousOperatorId}`,
          event.alarm_group_id,
        );
      }

      // Store the alarm data in the operator's hash
      multi.hset(operatorKey, event.alarm_group_id, serializedEvent);

      // Set the operator as the sole operator for this alarm (1:1 mapping)
      multi.set(alarmOperatorKey, event.operator_id);

      if (isEscalation) {
        multi.hset(
          `operator:${event.operator_id}:escalations`,
          event.alarm_group_id,
          serializedEvent,
        );
      }

      // Add expiry commands
      multi.expire(operatorKey, 86400); // 1 day expiry
      multi.expire(alarmOperatorKey, 86400);
      if (isEscalation) {
        multi.expire(`operator:${event.operator_id}:escalations`, 86400);
      }

      // Execute the transaction
      const results = await multi.exec();

      // If results is null, the transaction was aborted due to a watched key changing
      if (results === null) {
        throw new Error("Transaction aborted due to concurrent modification");
      }

      success = true;
      return true;
    } catch (error) {
      retryCount++;
      logger.warning("Redis operation failed, retrying", {
        attempt: retryCount,
        maxRetries,
        error: error instanceof Error ? error.message : String(error),
      });

      if (retryCount >= maxRetries) {
        logger.error("Redis operations failed after retries", {
          error: error instanceof Error ? error.message : String(error),
        });
        return false;
      }

      // Add exponential backoff delay between retries
      await new Promise((resolve) =>
        setTimeout(resolve, 2 ** retryCount * 100),
      );
    }
  }

  return false;
}
