import {
  type Escalation,
  type Frame,
  type Media,
  Product,
  type UnifiedAlarm,
  type UnifiedAlarmGroup,
  type UnifiedLocation,
} from "../types/alarm";
import type {
  AlarmGroup,
  EscalatedAlarmGroup,
  EventDetails,
} from "../types/alarm_groups";
import type { CameraDetails } from "../types/common";

export const convertAlarmGroupToUnifiedAlarmGroup = (
  alarmGroup: AlarmGroup,
  cameraDetailsMap: Map<string, CameraDetails>,
  tenantToEscalatedAlarmGroupsMap?: Map<string, EscalatedAlarmGroup[]>,
): UnifiedAlarmGroup => {
  // Convert EventDetails to UnifiedAlarm
  const alarms: UnifiedAlarm[] = (alarmGroup.events || []).map(
    (event: EventDetails) => {
      const cameraDetails = cameraDetailsMap.get(event.camera_id);
      // Convert EventFrame to Frame
      const frames: Frame[] = event.frames.map((frame) => ({
        url: frame.image_url,
        timestamp: frame.timestamp,
      }));

      const frameTimestamp = event.frames.map((frame) => frame.timestamp);
      frameTimestamp.sort();
      const eventStartTime = frameTimestamp[0];
      const eventEndTime = frameTimestamp[frameTimestamp.length - 1];

      // Create Media object
      const media: Media = {
        type: "frames",
        frames: frames,
      };

      return {
        id: event.id,
        alarmGroupId: alarmGroup.id,
        timestamp: event.event_time_utc,
        cameraId: event.camera_id,
        cameraName: event.camera_name,
        cameraGroupId: event.camera_group_id,
        cameraGroupName: cameraDetails?.camera_group_name ?? "",
        cameraLiveStreamUrl: cameraDetails?.livestream_url ?? "",
        cameraPlayBackUrl: cameraDetails?.playback_url ?? "",
        alarmStartTime: eventStartTime,
        alarmEndTime: eventEndTime,
        severity: event.severity,
        media: media,
        resolvedAlarm: false,
        isTalkDownEnabled: cameraDetails?.is_talkdown_enabled ?? false,
      };
    },
  );

  // Taking first alarms and its cameras location details
  const alarm_group_location =
    alarms.length > 0 ? cameraDetailsMap.get(alarms[0].cameraId) : null;
  // Create a minimal location object since AlarmGroup doesn't have location info
  const location: UnifiedLocation = {
    id: alarm_group_location?.location_id.toString() ?? "",
    name: alarm_group_location?.location_name ?? "",
    city: alarm_group_location?.location_city ?? "",
    state: alarm_group_location?.location_state ?? "",
    country: alarm_group_location?.location_country ?? "",
    description: alarm_group_location?.location_description ?? "",
    tenant_id: alarmGroup.tenant_id,
    timezone: alarm_group_location?.timezone ?? "",
  };

  const escalatedAlarmGroups =
    tenantToEscalatedAlarmGroupsMap?.get(alarmGroup.tenant_id) ?? [];
  const alarmGroupTenantEscalations: Escalation[] = escalatedAlarmGroups.map(
    (escalatedAlarmGroup) => ({
      id: escalatedAlarmGroup.id,
      title: "",
      description: "",
      initiatingLocationAlarmId: escalatedAlarmGroup.id,
      incidentStartTimeUtc: escalatedAlarmGroup.start_time_utc,
      incidentEndTimeUtc: "",
    }),
  );

  // Create empty escalations array since AlarmGroup doesn't have escalation info
  const escalations: Escalation[] = alarmGroupTenantEscalations;

  return {
    id: alarmGroup.id,
    timestamp: alarmGroup.start_time_utc,
    tenantId: alarmGroup.tenant_id,
    location: location,
    alarms: alarms,
    alarmCount: alarmGroup.events_count ?? 0,
    status: alarmGroup.state,
    severity: alarmGroup.severity,
    escalations: escalations,
    product: Product.SCAN,
    resolution: alarmGroup.resolution,
    tapScore: alarmGroup.severity.toLowerCase() === "high" ? 90 : 70,
  };
};
