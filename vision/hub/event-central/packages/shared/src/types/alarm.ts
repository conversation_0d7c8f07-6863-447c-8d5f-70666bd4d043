export interface Frame {
  url: string;
  timestamp: number;
}

export enum BoxType {
  PERSON = "PERSON",
  VEHICLE = "MOVING VEHICLE",
}
export interface Box {
  frame: number; // -1 if applicable to entire video
  coords: number[]; // Top left x, y, width, height
  type?: BoxType;
}

export interface AlarmVideo {
  boxes: Box[];
  url: string;
  startTime: string;
  endTime: string;
}

export interface AlarmMediaResponse extends AlarmVideo {
  alarmId: string;
}

export interface Media {
  type: string;
  video?: AlarmVideo;
  frames: Frame[];
}

export interface UnifiedAlarm {
  id: string;
  alarmGroupId: string;
  timestamp: string;
  cameraId: string;
  cameraName: string;
  cameraGroupId: string;
  cameraGroupName: string;
  cameraLiveStreamUrl: string;
  cameraPlayBackUrl: string;
  alarmStartTime: number;
  alarmEndTime: number;
  severity: string;
  media: Media;
  resolvedAlarm: boolean;
  isTalkDownEnabled: boolean;
}

export interface Escalation {
  id: string;
  title: string;
  description: string;
  incidentStartTimeUtc: string;
  incidentEndTimeUtc: string;
  initiatingLocationAlarmId: string;
}

export interface UnifiedLocation {
  id: string | number;
  name: string;
  city?: string;
  state?: string;
  country?: string;
  description?: string;
  tenant_id: string;
  timezone?: string;
}

export enum Product {
  SCAN = "scan",
  REMOTE_GUARDING = "remote-guarding",
}

export interface UnifiedAlarmGroup {
  id: string;
  timestamp: string;
  tenantId: string;
  location: UnifiedLocation;
  alarms: UnifiedAlarm[];
  alarmCount?: number;
  status: string;
  severity: string;
  product: Product;
  escalations: Escalation[];
  resolution?: string;
  tapScore?: number;
}

export const statuses = [
  "Resolved by Hakimo",
  "Analyzing",
  "Pending",
  "Resolved Manually",
  "Alarm Acknowledged via ACS",
  "In Progress",
] as const;

export type AlarmStatus = (typeof statuses)[number];
