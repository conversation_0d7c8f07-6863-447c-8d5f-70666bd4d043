export interface EventFrame {
  image_url: string;
  timestamp: number;
}

export interface EventDetails {
  alarm_group_id: string;
  camera_group_id: string;
  camera_id: string;
  camera_name: string;
  device_id: string;
  event_time_utc: string;
  frames: EventFrame[];
  id: string;
  severity: string;
  tenant_id: string;
}

export interface AlarmGroup {
  camera_group_id: string;
  created_at_utc: string;
  end_time_utc: string | null;
  events?: EventDetails[];
  events_count: number;
  id: string;
  operator_id: string;
  resolution: string;
  resolution_comment: string;
  severity: string;
  start_time_utc: string;
  state: string;
  tenant_id: string;
}

export interface EscalatedAlarmGroup {
  id: string;
  tenant_id: string;
  created_at_utc: string;
  start_time_utc: string;
  severity: string;
  state: string;
}

export interface AlarmGroupResponse {
  items: AlarmGroup[];
  total: number;
  page_size: number;
  page: number;
  items_count: number;
}

export interface AlarmGroupUpdateEventDetails {
  operator_id?: string;
  resolution_comment?: string;
  event_type?: string;
  group_id?: string;
  site_alarm_id?: string;
  tenant_id?: string;
  timestamp_utc?: number;
  event_id?: string;
}

export interface AlarmGroupUpdate {
  alarm_group_id: string;
  created_at_utc: string;
  event_details: AlarmGroupUpdateEventDetails;
  event_type: string;
  id: string;
}

export interface AlarmGroupStateUpdateResponse {
  payload: any;
  status: string;
  message: string;
}
