export enum HttpStatus {
  OK = 200,
  BAD_REQUEST = 400,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

export class HttpServerError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public originalError?: Error,
  ) {
    super(message);
    this.name = "HttpServerError";
  }
}

export interface CameraDetails {
  id: string;
  name: string;
  location_id: number;
  location_name: string;
  location_city: string;
  location_state: string;
  location_country: string;
  location_description: string;
  location_floorplan_url: string;
  livestream_url: string;
  playback_url: string;
  timezone: string;
  integration_type: string;
  is_talkdown_enabled: boolean;
  tenant_id: string;
  camera_group_id: string;
  camera_group_name: string;
  camera_group_description: string;
}
